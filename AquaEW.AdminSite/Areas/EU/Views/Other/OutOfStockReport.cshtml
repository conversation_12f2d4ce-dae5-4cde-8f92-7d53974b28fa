@using AquaEW.Core.Views.Shared.ViewComponents
@model List<AquaEW.Core.ViewModel.CategoryViewModel>
@{
    ViewData["Title"] = "Báo cáo hết hàng";
}

<div class="bg-light-bg flex flex-col gap-0 items-start justify-start self-stretch shrink-0 relative w-full">
    <div class="w-full">
        <h1 class="justify-center text-zinc-900 text-2xl font-semibold font-['Inter'] leading-loose">Báo cáo hết hàng</h1>
        <p class="font-normal text-base leading-[150%] tracking-[-0.01em] align-middle text-[#76787B] pb-3">Chọn danh mục sản phẩm để báo cáo hết hàng.</p>
        <div class="border-b border-[#E3E5E9] -mx-[2rem]"></div>
    </div>
    <div class="pt-6 pb-6 flex flex-col gap-6 items-start justify-start self-stretch shrink-0 relative">
        <div class="flex flex-col gap-4 items-start justify-start self-stretch shrink-0 relative">
            @{
                // Get categories from the controller (with proper icon paths)
                var categories = Model ?? new List<AquaEW.Core.ViewModel.CategoryViewModel>();

                // Organize into rows of 5
                var rows = new List<List<AquaEW.Core.ViewModel.CategoryViewModel>>();
                for (int i = 0; i < categories.Count; i += 5)
                {
                    rows.Add(categories.Skip(i).Take(5).ToList());
                }
            }

            @foreach (var row in rows)
            {
                <div class="flex flex-row gap-4 items-center justify-start self-stretch shrink-0 relative">
                    @foreach (var category in row)
                    {
                        <button type="button"
                                class="bg-grey-100 rounded-3xl pt-10 pr-6 pb-10 pl-6 flex flex-col gap-10 items-center justify-center flex-1 h-[235px] relative cursor-pointer hover:bg-[rgba(35,115,252,0.1)] transition-colors duration-200 border-1 border-transparent hover:border-[#2373FC]"
                                onclick="selectCategory('@category.Id')"
                                onkeydown="handleKeyDown(event, '@category.Id')"
                                aria-label="Chọn danh mục @category.Id">
                            <div class="pr-px pl-px flex flex-row gap-2 items-center justify-center shrink-0 w-[60px] h-[60px] relative overflow-hidden">
                                <img class="shrink-0 w-[60px] h-[60px] relative overflow-visible"
                                     src="@category.Icon"
                                     alt="@category.Name" />
                            </div>
                            <div class="flex flex-col gap-1 items-center justify-center shrink-0 relative">
                                <div class="text-grey-700 text-left font-text-regular-medium-font-family text-text-regular-medium-font-size leading-text-regular-medium-line-height font-text-regular-medium-font-weight relative flex items-center justify-start"
                                     style="letter-spacing: var(--text-regular-medium-letter-spacing, -0.01em);">
                                    @category.Name
                                </div>
                            </div>
                        </button>
                    }

                    @* Fill remaining columns if row is not complete *@
                    @for (int i = row.Count; i < 5; i++)
                    {
                        <div class="flex-1"></div>
                    }
                </div>
            }
        </div>
    </div>
</div>

<style>
    /* Custom fonts and spacing classes */
    .font-text-tiny-regular-font-family { font-family: var(--text-tiny-regular-font-family, system-ui, -apple-system, sans-serif); }
    .text-text-tiny-regular-font-size { font-size: var(--text-tiny-regular-font-size, 12px); }
    .leading-text-tiny-regular-line-height { line-height: var(--text-tiny-regular-line-height, 1.4); }
    .font-text-tiny-regular-font-weight { font-weight: var(--text-tiny-regular-font-weight, 400); }

    .font-text-tiny-medium-font-family { font-family: var(--text-tiny-medium-font-family, system-ui, -apple-system, sans-serif); }
    .text-text-tiny-medium-font-size { font-size: var(--text-tiny-medium-font-size, 12px); }
    .leading-text-tiny-medium-line-height { line-height: var(--text-tiny-medium-line-height, 1.4); }
    .font-text-tiny-medium-font-weight { font-weight: var(--text-tiny-medium-font-weight, 500); }

    .font-heading-h6-font-family { font-family: var(--heading-h6-font-family, system-ui, -apple-system, sans-serif); }
    .text-heading-h6-font-size { font-size: var(--heading-h6-font-size, 18px); }
    .leading-heading-h6-line-height { line-height: var(--heading-h6-line-height, 1.3); }
    .font-heading-h6-font-weight { font-weight: var(--heading-h6-font-weight, 600); }

    .font-text-regular-regular-font-family { font-family: var(--text-regular-regular-font-family, system-ui, -apple-system, sans-serif); }
    .text-text-regular-regular-font-size { font-size: var(--text-regular-regular-font-size, 14px); }
    .leading-text-regular-regular-line-height { line-height: var(--text-regular-regular-line-height, 1.5); }
    .font-text-regular-regular-font-weight { font-weight: var(--text-regular-regular-font-weight, 400); }

    .font-text-regular-medium-font-family { font-family: var(--text-regular-medium-font-family, system-ui, -apple-system, sans-serif); }
    .text-text-regular-medium-font-size { font-size: var(--text-regular-medium-font-size, 14px); }
    .leading-text-regular-medium-line-height { line-height: var(--text-regular-medium-line-height, 1.5); }
    .font-text-regular-medium-font-weight { font-weight: var(--text-regular-medium-font-weight, 500); }

    .font-text-compact-txt-compact-small-font-family { font-family: var(--text-compact-txt-compact-small-font-family, system-ui, -apple-system, sans-serif); }
    .text-text-compact-txt-compact-small-font-size { font-size: var(--text-compact-txt-compact-small-font-size, 11px); }
    .leading-text-compact-txt-compact-small-line-height { line-height: var(--text-compact-txt-compact-small-line-height, 1.3); }
    .font-text-compact-txt-compact-small-font-weight { font-weight: var(--text-compact-txt-compact-small-font-weight, 400); }

    /* Color classes */
    .bg-light-bg { background-color: #f8f9fa; }
    .border-grey-200 { border-color: #e5e7eb; }
    .text-grey-500 { color: #6b7280; }
    .text-grey-700 { color: #374151; }
    .text-foregrounds-fg-muted { color: #9ca3af; }
    .bg-grey-100 { background-color: #f3f4f6; }
    .bg-grey-200 { background-color: #e5e7eb; }

    /* Button styling */
    button.bg-grey-100:focus {
        outline: 2px solid #2563eb;
        outline-offset: 2px;
    }

    button.bg-grey-100:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
</style>

<script>
    function selectCategory(Id) {
        console.log('Selected plant ID:', Id);
        // Navigate to the detailed report using PlantId
        window.location.href = `/EU/Other/OutOfStockDetail/${Id}`;
    }

    function handleKeyDown(event, Id) {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            selectCategory(Id);
        }
    }

    // Add enhanced hover effects and accessibility
    // document.addEventListener('DOMContentLoaded', function() {
    //     const categoryButtons = document.querySelectorAll('button[aria-label*="Chọn danh mục"]');

    //     categoryButtons.forEach(button => {
    //         // Enhanced focus handling
    //         button.addEventListener('focus', function() {
    //             this.style.transform = 'translateY(-1px)';
    //             this.style.boxShadow = '0 2px 4px -1px rgba(0, 0, 0, 0.1)';
    //         });

    //         button.addEventListener('blur', function() {
    //             this.style.transform = 'translateY(0)';
    //             this.style.boxShadow = 'none';
    //         });

    //         // Reset hover effects
    //         button.addEventListener('mouseleave', function() {
    //             this.style.transform = 'translateY(0)';
    //             this.style.boxShadow = 'none';
    //         });
    //     });
    // });
</script>
