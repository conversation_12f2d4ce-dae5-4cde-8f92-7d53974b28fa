@{
    // Revoke SellIn Success Modal
    // Shows success message when sell in declaration is successfully revoked
}

<div class="modal fade" id="revokeSuccessModal" tabindex="-1" aria-labelledby="revokeSuccessModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-body p-0">
                <!-- Revoke success content with card design -->
                <div class="bg-white rounded-2xl p-8 flex flex-col gap-4 items-start justify-start relative">

                    <img class="shrink-0 w-[100px] h-[84px] relative overflow-visible" src="~/images/icons/modal-success.svg" alt="Success Icon" />

                    <div class="flex flex-col gap-2 items-start justify-start self-stretch shrink-0 relative">
                        <div class="pb-4 flex flex-row gap-6 items-center justify-start self-stretch shrink-0 relative">
                            <div class="flex flex-col gap-2 items-start justify-start flex-1 relative">
                                <div class="flex flex-row gap-2 items-center justify-start shrink-0 relative">
                                    <div class="text-sematic-green text-left font-['Inter-SemiBold',_sans-serif] text-xl leading-[140%] font-semibold relative flex items-center justify-start overflow-hidden"
                                         style="letter-spacing: -0.02em; text-overflow: ellipsis; white-space: nowrap;">
                                        Thu hồi thành công!
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-black text-left font-text-regular-regular-font-family text-text-regular-regular-font-size leading-text-regular-regular-line-height font-text-regular-regular-font-weight relative self-stretch flex items-center justify-start border-b border-gray-200 pb-4"
                         style="letter-spacing: var(--text-regular-regular-letter-spacing, -0.01em);">
                        Lệnh thu hồi khai báo bán hàng của bạn thành công!
                    </div>

                    <div class="flex flex-row gap-4 items-center justify-end self-stretch shrink-0 relative">
                        <div class="bg-primary-blue-500 rounded-lg pt-2 pr-4 pb-2 pl-4 flex flex-row gap-3 items-center justify-center shrink-0 h-10 relative cursor-pointer" id="understoodBtn" data-bs-dismiss="modal">
                            <div class="text-white text-left font-text-small-medium-font-family text-text-small-medium-font-size leading-text-small-medium-line-height font-text-small-medium-font-weight relative"
                                 style="letter-spacing: var(--text-small-medium-letter-spacing, -0.01em)" id="understoodBtnText">
                                Đã hiểu
                            </div>
                            <div class="hidden" id="understoodLoader">
                                <svg class="h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced modal styling for revoke success modal */
    #revokeSuccessModal.modal {
        background-color: rgba(0, 0, 0, 0.5); /* Dark semi-transparent backdrop */
        backdrop-filter: blur(4px); /* Add blur effect to background */
    }

    #revokeSuccessModal .modal-content {
        border-radius: 12px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: none;
    }

    /* Modal animation */

    /* Enhanced backdrop */
    #revokeSuccessModal.show {
        background-color: rgba(0, 0, 0, 0.6) !important;
    }

    /* Button states */
    #revokeSuccessModal .cursor-pointer:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    #revokeSuccessModal .transition-all {
        transition: none;
    }

    /* Design system tokens */
    #revokeSuccessModal .text-sematic-green {
        color: #059669; /* Green-600 */
    }

    #revokeSuccessModal .bg-primary-blue-500 {
        background-color: #3b82f6; /* Blue-500 */
    }

    #revokeSuccessModal .hover\:bg-blue-600:hover {
        background-color: #2563eb !important; /* Blue-600 */
    }

    #revokeSuccessModal .rounded-2xl {
        border-radius: 1rem; /* 16px */
    }

    #revokeSuccessModal .font-text-regular-regular-font-family {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #revokeSuccessModal .text-text-regular-regular-font-size {
        font-size: 14px;
    }

    #revokeSuccessModal .leading-text-regular-regular-line-height {
        line-height: 1.5;
    }

    #revokeSuccessModal .font-text-regular-regular-font-weight {
        font-weight: 400;
    }

    #revokeSuccessModal .font-text-small-medium-font-family {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #revokeSuccessModal .text-text-small-medium-font-size {
        font-size: 13px;
    }

    #revokeSuccessModal .leading-text-small-medium-line-height {
        line-height: 1.4;
    }

    #revokeSuccessModal .font-text-small-medium-font-weight {
        font-weight: 500;
    }

    /* Flexbox utilities for the design */
    #revokeSuccessModal .flex {
        display: flex;
    }

    #revokeSuccessModal .flex-col {
        flex-direction: column;
    }

    #revokeSuccessModal .flex-row {
        flex-direction: row;
    }

    #revokeSuccessModal .gap-2 {
        gap: 0.5rem;
    }

    #revokeSuccessModal .gap-3 {
        gap: 0.75rem;
    }

    #revokeSuccessModal .gap-4 {
        gap: 1rem;
    }

    #revokeSuccessModal .gap-6 {
        gap: 1.5rem;
    }

    #revokeSuccessModal .items-start {
        align-items: flex-start;
    }

    #revokeSuccessModal .items-center {
        align-items: center;
    }

    #revokeSuccessModal .justify-start {
        justify-content: flex-start;
    }

    #revokeSuccessModal .justify-center {
        justify-content: center;
    }

    #revokeSuccessModal .justify-end {
        justify-content: flex-end;
    }

    #revokeSuccessModal .self-stretch {
        align-self: stretch;
    }

    #revokeSuccessModal .shrink-0 {
        flex-shrink: 0;
    }

    #revokeSuccessModal .flex-1 {
        flex: 1 1 0%;
    }

    #revokeSuccessModal .relative {
        position: relative;
    }

    #revokeSuccessModal .overflow-visible {
        overflow: visible;
    }

    #revokeSuccessModal .text-left {
        text-align: left;
    }

    #revokeSuccessModal .leading-[140%] {
        line-height: 140%;
    }

    #revokeSuccessModal .font-semibold {
        font-weight: 600;
    }

    #revokeSuccessModal .text-xl {
        font-size: 1.25rem; /* 20px */
    }

    #revokeSuccessModal .rounded-lg {
        border-radius: 0.5rem; /* 8px */
    }

    #revokeSuccessModal .pt-2 {
        padding-top: 0.5rem;
    }

    #revokeSuccessModal .pr-4 {
        padding-right: 1rem;
    }

    #revokeSuccessModal .pb-2 {
        padding-bottom: 0.5rem;
    }

    #revokeSuccessModal .pl-4 {
        padding-left: 1rem;
    }

    #revokeSuccessModal .pb-4 {
        padding-bottom: 1rem;
    }

    #revokeSuccessModal .p-8 {
        padding: 2rem;
    }

    #revokeSuccessModal .h-10 {
        height: 2.5rem;
    }

    #revokeSuccessModal .h-2 {
        height: 0.5rem;
    }

    #revokeSuccessModal .w-[100px] {
        width: 100px;
    }

    #revokeSuccessModal .h-[84px] {
        height: 84px;
    }

    #revokeSuccessModal .cursor-pointer {
        cursor: pointer;
    }

    #revokeSuccessModal .text-white {
        color: white;
    }

    #revokeSuccessModal .text-black {
        color: black;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to show loading state for a button
        function showButtonLoading(buttonId, textId, loaderId) {
            const button = document.getElementById(buttonId);
            const text = document.getElementById(textId);
            const loader = document.getElementById(loaderId);

            if (button && text && loader) {
                button.style.pointerEvents = 'none';
                button.style.opacity = '0.6';
                text.style.display = 'none';
                loader.classList.remove('hidden');
            }
        }

        // Function to hide loading state for a button
        function hideButtonLoading(buttonId, textId, loaderId) {
            const button = document.getElementById(buttonId);
            const text = document.getElementById(textId);
            const loader = document.getElementById(loaderId);

            if (button && text && loader) {
                button.style.pointerEvents = 'auto';
                button.style.opacity = '1';
                text.style.display = 'block';
                loader.classList.add('hidden');
            }
        }

        // Handle understood button click
        const understoodBtn = document.getElementById('understoodBtn');
        if (understoodBtn) {
            understoodBtn.addEventListener('click', function() {
                // Show loading state briefly for visual feedback
                showButtonLoading('understoodBtn', 'understoodBtnText', 'understoodLoader');
                setTimeout(() => {
                    // Redirect to SellInList after loading
                    window.location.href = '/EU/ProductImport/SellInList';
                }, 300);
            });
        }

        // Enhanced modal backdrop effect
        const modal = document.getElementById('revokeSuccessModal');
        if (modal) {
            modal.addEventListener('shown.bs.modal', function() {
                // Add enhanced backdrop styling when modal is shown
                modal.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent body scroll
            });

            modal.addEventListener('hidden.bs.modal', function() {
                // Remove enhanced backdrop styling when modal is hidden
                modal.classList.remove('show');
                document.body.style.overflow = 'auto'; // Restore body scroll

                // Reset button states when modal is closed
                hideButtonLoading('understoodBtn', 'understoodBtnText', 'understoodLoader');
            });
        }
    });
</script>
