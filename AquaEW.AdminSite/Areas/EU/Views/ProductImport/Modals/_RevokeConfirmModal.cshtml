@{
    // Revoke SellIn Confirmation Modal
    // Shows confirmation dialog when user wants to revoke SellIn declaration
}

<div class="modal fade" id="revokeConfirmModal" tabindex="-1" aria-labelledby="revokeConfirmModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-body p-0">
                <!-- Revoke confirmation content with card design -->
                <div class="bg-white rounded-2xl p-8 flex flex-col gap-4 items-start justify-start relative">

                    <div class="flex flex-col gap-2 items-start justify-start self-stretch shrink-0 relative">
                        <div class="pb-4 flex flex-row gap-6 items-center justify-start self-stretch shrink-0 relative">
                            <div class="flex flex-col gap-2 items-start justify-start flex-1 relative">
                                <div class="flex flex-row gap-2 items-center justify-start shrink-0 relative">
                                    <div class="text-grey-700 text-left font-['Inter-SemiBold',_sans-serif] text-xl leading-[140%] font-semibold relative flex items-center justify-start overflow-hidden"
                                         style="letter-spacing: -0.02em; text-overflow: ellipsis; white-space: nowrap;">
                                        Xác nhận thu hồi khai báo
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col gap-4 items-start justify-start self-stretch shrink-0 relative border-b border-grey-200 pb-4">
                        <div class="text-black text-left font-text-regular-regular-font-family text-text-regular-regular-font-size leading-text-regular-regular-line-height font-text-regular-regular-font-weight relative self-stretch flex items-center justify-start"
                             style="letter-spacing: var(--text-regular-regular-letter-spacing, -0.01em);">
                            Bạn có chắc chắn muốn thu hồi khai báo sản phẩm này không? Thông tin đã khai báo sẽ không được lưu.
                        </div>
                    </div>

                    <div class="flex flex-row gap-4 items-center justify-end self-stretch shrink-0 relative">
                        <div class="bg-grey-200 rounded-lg pt-2 pr-4 pb-2 pl-4 flex flex-row gap-3 items-center justify-center shrink-0 h-10 relative cursor-pointer" id="cancelRevokeBtn" data-bs-dismiss="modal">
                            <div class="text-black text-left font-text-small-medium-font-family text-text-small-medium-font-size leading-text-small-medium-line-height font-text-small-medium-font-weight relative"
                                 style="letter-spacing: var(--text-small-medium-letter-spacing, -0.01em)" id="cancelRevokeBtnText">
                                Không
                            </div>
                            <div class="hidden" id="cancelRevokeLoader">
                                <svg class="h-4 w-4 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="bg-sematic-red rounded-lg pt-2 pr-4 pb-2 pl-4 flex flex-row gap-3 items-center justify-center shrink-0 h-10 relative cursor-pointer" id="confirmRevokeBtn">
                            <div class="text-white text-left font-text-small-medium-font-family text-text-small-medium-font-size leading-text-small-medium-line-height font-text-small-medium-font-weight relative"
                                 style="letter-spacing: var(--text-small-medium-letter-spacing, -0.01em)" id="confirmRevokeBtnText">
                                Xác nhận
                            </div>
                            <div class="hidden" id="confirmRevokeLoader">
                                <svg class="h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced modal styling for revoke sellin confirm modal */
    #revokeConfirmModal.modal {
        background-color: rgba(0, 0, 0, 0.5); /* Dark semi-transparent backdrop */
        backdrop-filter: blur(4px); /* Add blur effect to background */
    }

    #revokeConfirmModal .modal-content {
        border-radius: 12px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: none;
    }

    /* Modal animation */

    /* Enhanced backdrop */
    #revokeConfirmModal.show {
        background-color: rgba(0, 0, 0, 0.6) !important;
    }

    /* Button states */
    #revokeConfirmModal .cursor-pointer:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    #revokeConfirmModal .transition-all {
        transition: none;
    }

    /* Design system tokens */
    #revokeConfirmModal .bg-grey-200 {
        background-color: #e5e7eb; /* Gray-200 */
    }

    #revokeConfirmModal .bg-sematic-red {
        background-color: #dc2626; /* Red-600 */
    }

    #revokeConfirmModal .hover\:bg-gray-300:hover {
        background-color: #d1d5db !important; /* Gray-300 */
    }

    #revokeConfirmModal .hover\:bg-red-700:hover {
        background-color: #b91c1c !important; /* Red-700 */
    }

    /* Custom styles for the revoke confirmation modal */
    #revokeConfirmModal .modal-content {
        border-radius: 12px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    #revokeConfirmModal .text-grey-700 {
        color: #374151; /* Gray-700 */
    }

    #revokeConfirmModal .bg-grey-200 {
        background-color: #e5e7eb; /* Gray-200 */
    }

    #revokeConfirmModal .bg-sematic-red {
        background-color: #dc2626; /* Red-600 */
    }

    #revokeConfirmModal .rounded-2xl {
        border-radius: 1rem; /* 16px */
    }

    #revokeConfirmModal .font-text-regular-regular-font-family {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #revokeConfirmModal .text-text-regular-regular-font-size {
        font-size: 14px;
    }

    #revokeConfirmModal .leading-text-regular-regular-line-height {
        line-height: 1.5;
    }

    #revokeConfirmModal .font-text-regular-regular-font-weight {
        font-weight: 400;
    }

    #revokeConfirmModal .font-text-small-medium-font-family {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #revokeConfirmModal .text-text-small-medium-font-size {
        font-size: 13px;
    }

    #revokeConfirmModal .leading-text-small-medium-line-height {
        line-height: 1.4;
    }

    #revokeConfirmModal .font-text-small-medium-font-weight {
        font-weight: 500;
    }

    /* Flexbox utilities for the design */
    #revokeConfirmModal .flex {
        display: flex;
    }

    #revokeConfirmModal .flex-col {
        flex-direction: column;
    }

    #revokeConfirmModal .flex-row {
        flex-direction: row;
    }

    #revokeConfirmModal .gap-2 {
        gap: 0.5rem;
    }

    #revokeConfirmModal .gap-3 {
        gap: 0.75rem;
    }

    #revokeConfirmModal .gap-4 {
        gap: 1rem;
    }

    #revokeConfirmModal .gap-6 {
        gap: 1.5rem;
    }

    #revokeConfirmModal .items-start {
        align-items: flex-start;
    }

    #revokeConfirmModal .items-center {
        align-items: center;
    }

    #revokeConfirmModal .justify-start {
        justify-content: flex-start;
    }

    #revokeConfirmModal .justify-center {
        justify-content: center;
    }

    #revokeConfirmModal .justify-end {
        justify-content: flex-end;
    }

    #revokeConfirmModal .self-stretch {
        align-self: stretch;
    }

    #revokeConfirmModal .shrink-0 {
        flex-shrink: 0;
    }

    #revokeConfirmModal .flex-1 {
        flex: 1 1 0%;
    }

    #revokeConfirmModal .relative {
        position: relative;
    }

    #revokeConfirmModal .overflow-visible {
        overflow: visible;
    }

    #revokeConfirmModal .text-left {
        text-align: left;
    }

    #revokeConfirmModal .leading-[140%] {
        line-height: 140%;
    }

    #revokeConfirmModal .font-semibold {
        font-weight: 600;
    }

    #revokeConfirmModal .text-xl {
        font-size: 1.25rem; /* 20px */
    }

    #revokeConfirmModal .rounded-lg {
        border-radius: 0.5rem; /* 8px */
    }

    #revokeConfirmModal .pt-2 {
        padding-top: 0.5rem;
    }

    #revokeConfirmModal .pr-4 {
        padding-right: 1rem;
    }

    #revokeConfirmModal .pb-2 {
        padding-bottom: 0.5rem;
    }

    #revokeConfirmModal .pl-4 {
        padding-left: 1rem;
    }

    #revokeConfirmModal .pb-4 {
        padding-bottom: 1rem;
    }

    #revokeConfirmModal .p-8 {
        padding: 2rem;
    }

    #revokeConfirmModal .h-10 {
        height: 2.5rem;
    }

    #revokeConfirmModal .h-2 {
        height: 0.5rem;
    }

    #revokeConfirmModal .cursor-pointer {
        cursor: pointer;
    }

    #revokeConfirmModal .text-white {
        color: white;
    }

    #revokeConfirmModal .text-black {
        color: black;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to show loading state for a button
        function showButtonLoading(buttonId, textId, loaderId) {
            const button = document.getElementById(buttonId);
            const text = document.getElementById(textId);
            const loader = document.getElementById(loaderId);

            if (button && text && loader) {
                button.style.pointerEvents = 'none';
                button.style.opacity = '0.6';
                text.style.display = 'none';
                loader.classList.remove('hidden');
            }
        }

        // Function to hide loading state for a button
        function hideButtonLoading(buttonId, textId, loaderId) {
            const button = document.getElementById(buttonId);
            const text = document.getElementById(textId);
            const loader = document.getElementById(loaderId);

            if (button && text && loader) {
                button.style.pointerEvents = 'auto';
                button.style.opacity = '1';
                text.style.display = 'block';
                loader.classList.add('hidden');
            }
        }

        // Handle cancel button click
        const cancelRevokeBtn = document.getElementById('cancelRevokeBtn');
        if (cancelRevokeBtn) {
            cancelRevokeBtn.addEventListener('click', function() {
                // Show loading state briefly for visual feedback
                showButtonLoading('cancelRevokeBtn', 'cancelRevokeBtnText', 'cancelRevokeLoader');

                setTimeout(() => {
                    hideButtonLoading('cancelRevokeBtn', 'cancelRevokeBtnText', 'cancelRevokeLoader');
                    // Navigate to sellin detail
                    const sellInId = window.duplicateMachineSellInId;
                    if (sellInId) {
                        window.location.href = '/EU/ProductImport/Detail/' + sellInId;
                    } else {
                        window.location.href = '@Url.Action("SellInList", "ProductImport")';
                    }
                }, 200);
            });
        }

        // Handle confirm revoke button click
        const confirmRevokeBtn = document.getElementById('confirmRevokeBtn');
        if (confirmRevokeBtn) {
            confirmRevokeBtn.addEventListener('click', function() {
                // Show loading state
                showButtonLoading('confirmRevokeBtn', 'confirmRevokeBtnText', 'confirmRevokeLoader');

                const sellInId = window.duplicateMachineSellInId;
                // Call revoke endpoint by sennInId
                fetch('/EU/ProductImport/RevokeSellIn', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `sellInId=${encodeURIComponent(sellInId)}`
                })
                .then(response => response.json())
                .then(data => {
                    // Hide loading
                    hideButtonLoading('confirmRevokeBtn', 'confirmRevokeBtnText', 'confirmRevokeLoader');
                    // Close confirm modal
                    const confirmModal = bootstrap.Modal.getInstance(document.getElementById('revokeConfirmModal'));
                    if (confirmModal) confirmModal.hide();
                    // Show revoke success modal
                    const successModal = new bootstrap.Modal(document.getElementById('revokeSuccessModal'));
                    successModal.show();
                })
                .catch(err => {
                    console.error('Revoke error', err);
                    hideButtonLoading('confirmRevokeBtn', 'confirmRevokeBtnText', 'confirmRevokeLoader');
                    alert('Không thể thu hồi khai báo, vui lòng thử lại');
                });
            });
        }

        // Enhanced modal backdrop effect
        const modal = document.getElementById('revokeConfirmModal');
        if (modal) {
            modal.addEventListener('shown.bs.modal', function() {
                // Add enhanced backdrop styling when modal is shown
                modal.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent body scroll
            });

            modal.addEventListener('hidden.bs.modal', function() {
                // Remove enhanced backdrop styling when modal is hidden
                modal.classList.remove('show');
                document.body.style.overflow = 'auto'; // Restore body scroll

                // Reset button states when modal is closed
                hideButtonLoading('cancelRevokeBtn', 'cancelRevokeBtnText', 'cancelRevokeLoader');
                hideButtonLoading('confirmRevokeBtn', 'confirmRevokeBtnText', 'confirmRevokeLoader');
            });
        }
    });
</script>
