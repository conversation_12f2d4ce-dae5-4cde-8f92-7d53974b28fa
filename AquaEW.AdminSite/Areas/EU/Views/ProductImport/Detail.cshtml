@using AquaEW.Core.Views.Shared.ViewComponents
@using AquaEW.Core.EnumModel
@using AquaEW.Core.Helper
@model Mio.Core.ViewModel.SellInViewModel

@{
    ViewData["Title"] = "Thông tin nhập hàng";
    var closeAtt = new Dictionary<string, object>() { ["data-bs-dismiss"] = "modal" };
    var breadcrumbItems = new List<BreadcrumbViewComponent.BreadcrumbItem>
    {
        new("Quản lý Nhập hàng", Url.Action("SellInList", "ProductImport", new { area = "EU" }), false),
        new("Thông tin nhập hàng", null, true),
    };

    var shopTypeId = Model?.ShopTypeId;
    var shopType = shopTypeId.HasValue ? ((EnumEW.EShopType)shopTypeId.Value).GetDisplayName() : "-";

    var regionId = Model?.RegionId;
    string regionDisplay = regionId.HasValue ? ((EnumBasic.RegionEnum)regionId.Value).GetDisplayName() : "-";

    var customerName = Model?.CustomerName;
    var customerId = Model?.CustomerId;

    var history = ViewData["history"] as List<Mio.Core.ViewModel.SellInStatusHistoryViewModel>;

    var index = 0;
    var statusHistoryItems = history?.Select(x => new HistoryItemModel()
    {
        Status = x.SellInStatus?.Name switch
        {
            "success" => StatusType.Success,
            "pending" => StatusType.Pending,
            "pending-cancel" => StatusType.Pending,
            "cancelled" => StatusType.Cancelled,
            "failed" => StatusType.Failed,
            "rejected" => StatusType.Rejected,
            _ => StatusType.Pending
        },
        CustomText = x.SellInStatus?.Remarks,
        Message = x.Content,
        Timestamp = x.CreatedAt.ToUniversalTime(),
        Position = index++,
        BadgeSize = BadgeSize.Large
    }).ToList();

    var rejectCount = Model?.SellInStatusHistories?.Count(h => h.SellInStatus?.Name == "reject") ?? 0;

    bool isShownCancellationFiles = Model.SellInStatusHistories.Any(h =>
                                        h.SellInStatusId == (int)EnumEW.ESellInStatus.PendingCancel) ||
                                    Model.SellInStatusHistories.Any(h =>
                                        h.SellInStatusId == (int)EnumEW.ESellInStatus.Cancelled);
}

<div>
    <div class="flex justify-between items-center">
        <div>
            <vc:breadcrumb items="@breadcrumbItems"></vc:breadcrumb>
            <h4 class="mt-2 text-2xl font-medium text-grey-700">@ViewBag.Title</h4>
            <div class="flex gap-2 items-center mt-2">
                <p class="text-secondary">Trạng thái khai báo nhập hàng</p>
                @switch (Model?.SellInStatusName)
                {
                    case "success":
                        <vc:status-badge status="Success" size="Small" text="THÀNH CÔNG"/>
                        break;
                    case "pending":
                        <vc:status-badge status="Pending" size="Small" text="CHỜ DUYỆT"/>
                        break;
                    case "pending-cancel":
                        <vc:status-badge status="Pending" size="Small" text="YÊU CẦU HUỶ"/>
                        break;
                    case "cancelled":
                        <vc:status-badge status="Cancelled" size="Small" text="HUỶ THÀNH CÔNG"/>
                        break;
                    case "reject":
                        <vc:status-badge status="Failed" size="Small" text="TỪ CHỐI"/>
                        break;
                    case "failed":
                        <vc:status-badge status="Failed" size="Small" text="KHÔNG THÀNH CÔNG"/>
                        break;
                }
            </div>
        </div>
        <div class="flex gap-2">
            @switch (Model?.SellInStatusName)
            {
                case "success":
                    <vc:button id="btnPendingCancel" variant="secondary" text="Hủy nhập hàng"/>
                    <vc:button id="btnViewReward" variant="primary" text="Xem thưởng"/>
                    break;
                case "pending":
                    <vc:button id="btnReject" variant="secondary" text="Từ chối"/>
                    <vc:button id="btnApprove" variant="primary" text="Duyệt"/>
                    break;
                case "pending-cancel":
                    <vc:button id="btnRejectCancel" variant="secondary" text="Từ chối huỷ"/>
                    <vc:button id="btnApproveCancel" variant="primary" text="Duyệt huỷ"/>
                    break;
                case "failed":
                    <vc:button id="btnRevoke" variant="secondary" text="Thu hồi nhập hàng"/>
                    break;
            }

        </div>
    </div>
    <div class="mt-4 -mr-8 mb-4 -ml-8 border-t border-gray-300"></div>
    <div class="flex">
        <div class="pr-8 border-r basis-2/3 border-grey-200">
            <div class="pt-8">
                @* Thong tin sản phẩm *@
                <div class="flex justify-between items-center mb-4">
                    <div class="flex gap-2 items-center">
                        <svg width="29" height="28" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="0.5" width="28" height="28" rx="14" fill="#D5E4FF"/>
                            <path
                                d="M21.8125 11.75C21.8128 11.6977 21.8057 11.6456 21.7914 11.5953L20.7824 8.06562C20.7146 7.83142 20.5728 7.62544 20.3783 7.47844C20.1838 7.33144 19.9469 7.2513 19.7031 7.25H9.29688C9.05306 7.2513 8.8162 7.33144 8.62168 7.47844C8.42715 7.62544 8.2854 7.83142 8.21758 8.06562L7.2093 11.5953C7.19477 11.6456 7.18743 11.6977 7.1875 11.75V12.875C7.1875 13.3116 7.28916 13.7423 7.48442 14.1328C7.67969 14.5233 7.9632 14.863 8.3125 15.125V19.625C8.3125 19.9234 8.43103 20.2095 8.64201 20.4205C8.85298 20.6315 9.13913 20.75 9.4375 20.75H19.5625C19.8609 20.75 20.147 20.6315 20.358 20.4205C20.569 20.2095 20.6875 19.9234 20.6875 19.625V15.125C21.0368 14.863 21.3203 14.5233 21.5156 14.1328C21.7108 13.7423 21.8125 13.3116 21.8125 12.875V11.75ZM9.29688 8.375H19.7031L20.5061 11.1875H8.49602L9.29688 8.375ZM12.8125 12.3125H16.1875V12.875C16.1875 13.3226 16.0097 13.7518 15.6932 14.0682C15.3768 14.3847 14.9476 14.5625 14.5 14.5625C14.0524 14.5625 13.6232 14.3847 13.3068 14.0682C12.9903 13.7518 12.8125 13.3226 12.8125 12.875V12.3125ZM11.6875 12.3125V12.875C11.6875 13.3226 11.5097 13.7518 11.1932 14.0682C10.8768 14.3847 10.4476 14.5625 10 14.5625C9.55245 14.5625 9.12323 14.3847 8.80676 14.0682C8.49029 13.7518 8.3125 13.3226 8.3125 12.875V12.3125H11.6875ZM19.5625 19.625H9.4375V15.6312C9.62268 15.6686 9.8111 15.6874 10 15.6875C10.4366 15.6875 10.8673 15.5858 11.2578 15.3906C11.6483 15.1953 11.988 14.9118 12.25 14.5625C12.512 14.9118 12.8517 15.1953 13.2422 15.3906C13.6327 15.5858 14.0634 15.6875 14.5 15.6875C14.9366 15.6875 15.3673 15.5858 15.7578 15.3906C16.1483 15.1953 16.488 14.9118 16.75 14.5625C17.012 14.9118 17.3517 15.1953 17.7422 15.3906C18.1327 15.5858 18.5634 15.6875 19 15.6875C19.1889 15.6874 19.3773 15.6686 19.5625 15.6312V19.625ZM19 14.5625C18.5524 14.5625 18.1232 14.3847 17.8068 14.0682C17.4903 13.7518 17.3125 13.3226 17.3125 12.875V12.3125H20.6875V12.875C20.6875 13.3226 20.5097 13.7518 20.1932 14.0682C19.8768 14.3847 19.4476 14.5625 19 14.5625Z"
                                fill="#003DA5"/>
                        </svg>

                        <span class="text-xs font-semibold uppercase text-primary-500">Thông tin sản phẩm</span>
                    </div>
                </div>

                <ul class="space-y-3">
                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Số máy:</span>
                        <span data-value="code">@(Model?.BarcodeStr ?? "-")</span>
                    </li>

                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Kiểu máy:</span>
                        <span data-value="name">@(Model?.ModelDisplay ?? "-")</span>
                    </li>

                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Ngành hàng:</span>
                        <span data-value="saleAreaDisplayName">@(Model?.PlantDisplay ?? "-")</span>
                    </li>
                </ul>
            </div>
            <div class="pt-8">
                @* Thong tin nhập hàng *@
                <div class="flex justify-between items-center mb-4">
                    <div class="flex gap-2 items-center">
                        <svg width="29" height="28" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="0.5" width="28" height="28" rx="14" fill="#D5E4FF"/>
                            <path
                                d="M17.5 10.25C17.5 9.55252 17.5 9.20378 17.4233 8.91766C17.2153 8.1412 16.6088 7.53472 15.8323 7.32667C15.5462 7.25 15.1975 7.25 14.5 7.25C13.8025 7.25 13.4538 7.25 13.1677 7.32667C12.3912 7.53472 11.7847 8.1412 11.5767 8.91766C11.5 9.20378 11.5 9.55252 11.5 10.25M9.4 20.75H19.6C20.4401 20.75 20.8601 20.75 21.181 20.5865C21.4632 20.4427 21.6927 20.2132 21.8365 19.931C22 19.6101 22 19.1901 22 18.35V12.65C22 11.8099 22 11.3899 21.8365 11.069C21.6927 10.7868 21.4632 10.5573 21.181 10.4135C20.8601 10.25 20.4401 10.25 19.6 10.25H9.4C8.55992 10.25 8.13988 10.25 7.81901 10.4135C7.53677 10.5573 7.3073 10.7868 7.16349 11.069C7 11.3899 7 11.8099 7 12.65V18.35C7 19.1901 7 19.6101 7.16349 19.931C7.3073 20.2132 7.53677 20.4427 7.81901 20.5865C8.13988 20.75 8.55992 20.75 9.4 20.75Z"
                                stroke="#003DA5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>

                        <span class="text-xs font-semibold uppercase text-primary-500">Thông tin nhập hàng</span>
                    </div>

                    @if (Model?.SellInStatusName == "failed")
                    {
                        <vc:button
                            id="sell-in-edit-btn"
                            text="Chỉnh sửa"
                            variant="outlined-secondary"
                            icon-left='
                            <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M10.5 16.6662H18M3 16.6662H4.39545C4.8031 16.6662 5.00693 16.6662 5.19874 16.6202C5.3688 16.5793 5.53138 16.512 5.6805 16.4206C5.84869 16.3175 5.99282 16.1734 6.28107 15.8852L16.75 5.4162C17.4404 4.72585 17.4404 3.60656 16.75 2.9162C16.0597 2.22585 14.9404 2.22585 14.25 2.9162L3.78105 13.3852C3.4928 13.6734 3.34867 13.8175 3.2456 13.9857C3.15422 14.1348 3.08688 14.2974 3.04605 14.4675C3 14.6593 3 14.8631 3 15.2708V16.6662Z" stroke="#1A1919" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        '
                        >
                        </vc:button>
                    }
                </div>

                <ul id="sell-in-info" class="space-y-3">
                    @if (Model?.StatusId == (int)EnumEW.ESellInStatus.Success ||
                         Model?.StatusId == (int)EnumEW.ESellInStatus.PendingCancel ||
                         Model?.StatusId == (int)EnumEW.ESellInStatus.Cancelled)
                    {
                        <li class="flex">
                            <span class="font-normal basis-1/3 shrink-0">Mã nhập hàng:</span>
                            <span data-value="representer">@(Model?.SellInCode ?? "-")</span>
                        </li>
                    }
                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Ngày nhập hàng:</span>
                        <span data-value="representer">@(Model?.DeclarationDate?.ToString("dd/MM/yyyy") ?? "-")</span>
                    </li>

                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Kênh phân phối:</span>
                        <span data-value="taxCode">@shopType</span>
                    </li>

                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Khu vực phân phối:</span>
                        <span data-value="gpkd">@regionDisplay</span>
                    </li>

                    <li class="flex">
                        <span class="font-normal basis-1/3 shrink-0">Nhà phân phối:</span>
                        <span data-value="businessTypeFmt">@(customerName ?? "-")</span>
                    </li>
                </ul>
            </div>

            @* Thong tin chứng từ *@
            @if (Model?.SellInStatusName == "pending" && Model.SellInFiles != null && Model.SellInFiles.Any())
            {
                <div class="overflow-hidden pt-8 mb-6">
                    <div class="flex items-center mb-2">
                        <div class="flex justify-center items-center mr-2 w-7 h-7 bg-blue-200 rounded-full">
                            <img src="~/images/icons/file-check.svg" alt="Customer Icon" class="w-4 h-4 text-white">
                        </div>
                        <h2 class="text-xs font-semibold text-primary-500">THÔNG TIN CHỨNG TỪ</h2>
                    </div>
                    <div class="p-2">
                        @{
                            var completedFiles = Model.SellInFiles.Where(wf => wf.EwFile != null).ToList();
                            var completedInvoiceFiles = completedFiles.Where(wf => wf.Type == "invoice").ToList();
                            var completedBarcodeFiles = completedFiles.Where(wf => wf.Type == "barcode").ToList();
                        }

                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div class="flex flex-col">
                                <h3 class="text-sm font-medium text-gray-700 mb-3 h-[44px]">Hình ảnh Hóa đơn</h3>
                                <div class="space-y-2">
                                    @if (completedInvoiceFiles.Any())
                                    {
                                        @foreach (var file in completedInvoiceFiles)
                                        {
                                            <div class="border rounded-lg bg-gray-50 overflow-hidden h-[176px]">
                                                <img src="@file.EwFile.Url" alt="Image"
                                                     class="object-cover w-full h-full">
                                            </div>
                                        }
                                    }
                                </div>
                            </div>


                            <div class="flex flex-col">
                                <h3 class="text-sm font-medium text-gray-700 mb-3 h-[44px]">Hình ảnh Số máy</h3>
                                <div class="space-y-2">
                                    @if (completedBarcodeFiles.Any())
                                    {
                                        @foreach (var file in completedBarcodeFiles)
                                        {
                                            <div class="border rounded-lg bg-gray-50 overflow-hidden h-[176px]">
                                                <img src="@file.EwFile.Url" alt="Image"
                                                     class="object-cover w-full h-full">
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            @if (isShownCancellationFiles)
            {
                <div class="pt-8 mb-6 overflow-hidden">
                    <div class="flex items-center mb-2">
                        <div class="h-7 w-7 rounded-full mr-2 flex items-center justify-center">
                            <img src="~/images/icons/file-document.svg" alt="Customer Icon"
                                 class="h-20 w-20 text-white">
                        </div>
                        <h2 class="text-xs font-semibold text-primary-500">THÔNG TIN HỦY KHAI BÁO</h2>
                    </div>
                    @if (rejectCount >= 3)
                    {
                        <p class="!my-4 font-inter font-normal text-[14px] text-[#E20E16] leading-[1.5] tracking-[-0.01em]">
                            Bạn đã bị AQUA từ chối huỷ khai báo bán hàng 3 lần liên tiếp. Bạn không thể tải lên các
                            chứng từ!</p>
                    }
                    <div class="p-2 flex flex-col space-y-4">
                        @if (!string.IsNullOrEmpty(Model.Reason))
                        {
                            <div class="inline-flex items-center gap-2">
                                <div class="text-sm font-normal text-gray-500">Lý do huỷ:</div>
                                <div class="text-black pl-24">@Model.Reason</div>
                            </div>
                        }
                        <div class="text-sm font-normal text-gray-500">Chứng từ hủy khai báo:</div>

                        <div id="fileList" class="grid grid-cols-2 gap-3 w-full">
                            @foreach (var file in Model.SellInFiles.Where(f => f.EwFile != null && f.Type == "cancellation"))
                            {
                                var fileViewModel = new FileViewModel
                                {
                                    Name = file.Remark ?? file.EwFile.Name,
                                    Extension = file.EwFile.Extension,
                                    Size = file.EwFile.SizeInByte,
                                    UploadDate = file.EwFile.CreatedAt.ToString("dd/MM/yyyy"),
                                    Url = file.EwFile.Url,
                                    ThumbnailUrl = file.EwFile.Url
                                };
                                <div class="col-span-1">
                                    <partial name="~/Views/Shared/Components/_FilePreview.cshtml"
                                             model="fileViewModel"/>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            @if (Model?.SellInStatusName == "failed" || Model?.SellInStatusName == "reject")
            {
                <div class="overflow-hidden pt-8 mb-6">
                    <div class="flex items-center mb-2">
                        <div class="flex justify-center items-center mr-3 w-8 h-8 bg-blue-200 rounded-full">
                            <img src="~/images/icons/file-check.svg" alt="Customer Icon" class="w-4 h-4 text-white">
                        </div>
                        <span class="text-xs font-medium text-blue-800">THÔNG TIN CHỨNG TỪ</span>
                    </div>
                    @if (Model?.SellInStatusName == "cancel-reject" || Model?.SellInStatusName == "reject")
                    {
                        <p class="!my-4 font-inter font-normal text-[14px] text-[#E20E16] leading-[1.5] tracking-[-0.01em]">
                            @if (rejectCount < 10)
                            {
                                @:⚠ Vui lòng tải lên chứng từ khác, các chứng từ bạn tải lên đã bị admin từ chối.
                            }
                            else
                            {
                                @:"⚠ Bạn đã bị AQUA từ chối khai báo bán hàng 10 lần liên tiếp. Bạn không thể tải lên các chứng từ";
                            }
                        </p>
                    }
                    <div class="p-2">
                        @{
                            // Get existing files from SellInFiles and group by type
                            var existingFiles = Model?.SellInFiles?.Where(wf => wf.EwFile != null).ToList() ?? new List<Mio.Core.ViewModel.SellInFileViewModel>();

                            var invoiceFiles = existingFiles.Where(wf => wf.Type == "invoice")
                                .Select(wf => new FileViewModel
                                {
                                    Name = wf.EwFile.Name,
                                    Extension = wf.EwFile.Extension,
                                    Size = 0, // Size info not available in current model
                                    UploadDate = DateTime.Now.ToString("dd/MM/yyyy"),
                                    Url = wf.EwFile.Url,
                                    ThumbnailUrl = wf.EwFile.Url
                                }).ToList();

                            var barcodeFiles = existingFiles.Where(wf => wf.Type == "barcode")
                                .Select(wf => new FileViewModel
                                {
                                    Name = wf.EwFile.Name,
                                    Extension = wf.EwFile.Extension,
                                    Size = 0,
                                    UploadDate = DateTime.Now.ToString("dd/MM/yyyy"),
                                    Url = wf.EwFile.Url,
                                    ThumbnailUrl = wf.EwFile.Url
                                }).ToList();
                        }

                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <!-- Hình ảnh Hóa đơn -->
                            <div class="flex flex-col justify-between">
                                <label for="invoiceFileUpload" class="mb-2 text-sm font-normal text-gray-700">Hình
                                    ảnh Hóa đơn <span class="text-red-500">*</span></label>
                                <vc:image-upload
                                    name="invoiceFile"
                                    id="invoiceFileUpload"
                                    multiple="false"
                                    required="@(invoiceFiles.Count == 0)"
                                    accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
                                    initial-files="@invoiceFiles"
                                    max-file-size="10"
                                    custom-class="warranty-invoice-upload"
                                    warranty-id="@Model?.SellInId.ToString()"/>
                            </div>

                            <!-- Hình ảnh Số máy -->
                            <div class="flex flex-col justify-between">
                                <label for="barcodeFileUpload" class="mb-2 text-sm font-normal text-gray-700">Hình
                                    ảnh Số máy <span class="text-red-500">*</span></label>
                                <vc:image-upload
                                    name="barcodeFile"
                                    id="barcodeFileUpload"
                                    multiple="false"
                                    required="@(barcodeFiles.Count == 0)"
                                    accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
                                    initial-files="@barcodeFiles"
                                    max-file-size="10"
                                    custom-class="warranty-barcode-upload"
                                    warranty-id="@Model?.SellInId.ToString()"/>
                            </div>
                        </div>

                        @if (Model?.SellInStatusName != "pending" && rejectCount < 10)
                        {
                            <div class="flex justify-end items-center">
                                <button
                                    id="submitDocumentsBtn"
                                    type="button"
                                    class="flex relative flex-row gap-3 items-center pt-2 pr-4 pb-2 pl-4 h-10 text-white bg-blue-800 rounded-lg transition-colors hover:bg-blue-900 disabled:bg-gray-400 disabled:cursor-not-allowed shrink-0 w-fit"
                                >
                                    <span id="submitBtnSpinner" class="hidden">
                                        <svg class="w-4 h-4 text-white animate-spin"
                                             xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </span>
                                    <span id="submitBtnText">Gửi chứng từ</span>
                                </button>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

        <div class="p-8">
            <div class="flex gap-2 items-center mb-4">
                <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="28" height="28" rx="14" fill="#D5E4FF"/>
                    <path
                        d="M22.025 13.625L20.5254 15.125L19.025 13.625M20.7088 14.75C20.736 14.5037 20.75 14.2535 20.75 14C20.75 10.2721 17.7279 7.25 14 7.25C10.2721 7.25 7.25 10.2721 7.25 14C7.25 17.7279 10.2721 20.75 14 20.75C16.1205 20.75 18.0125 19.7722 19.25 18.243M14 10.25V14L16.25 15.5"
                        stroke="#003DA5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>

                <span class="text-xs font-semibold uppercase text-primary-500">LỊCH SỬ TRẠNG THÁI</span>
            </div>

            <vc:history
                items="statusHistoryItems"
                show-filters="false"
                enable-live-refresh="false"
                entity-id="1"
                time-format="dd/MM/yyyy HH:mm"></vc:history>
        </div>
    </div>
</div>

<!-- Sell-In Edit Modal -->
<div class="modal fade" id="modal-sell-in-edit">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="relative modal-content">
            <!-- Add custom styles for date picker dropdown inside modal -->
            <style>
                /* Fix z-index issues for date picker dropdown inside modal */
                #modal-sell-in-edit .calendar-container {
                    z-index: 9999 !important; /* Higher than modal backdrop */
                    position: absolute !important;
                }

                /* Ensure dropdown appears above modal content */
                #modal-sell-in-edit .single-date-picker {
                    z-index: 9999 !important;
                }

                /* Fix positioning for date picker in modal */
                #modal-sell-in-edit .calendar-input-container {
                    position: relative !important;
                }

                /* Ensure modal backdrop doesn't interfere */
                #modal-sell-in-edit.modal {
                    z-index: 1050 !important;
                }

                #modal-sell-in-edit .modal-dialog {
                    z-index: 1055 !important;
                }

                #modal-sell-in-edit .modal-content {
                    z-index: 1060 !important;
                }

                /* Additional fixes for dropdown positioning */
                #modal-sell-in-edit .calendar-container.above {
                    z-index: 9999 !important;
                }

                /* Ensure dropdown doesn't get clipped by modal overflow */
                #modal-sell-in-edit .modal-body {
                    overflow: visible !important;
                }

                #modal-sell-in-edit .modal-content {
                    overflow: visible !important;
                }
            </style>

            <form id="sell-in-edit-form">
                <div class="p-8">
                    <h4 class="mt-2 modal-title">Chỉnh sửa thông tin nhập hàng</h4>
                </div>
                <div class="px-8 py-0 modal-body">
                    <div class="grid grid-cols-2 gap-4">
                        <input type="hidden" name="SellInId" value="@Model?.SellInId"/>
                        <div>
                            <p>Ngày nhập hàng <span class="text-red-500">*</span></p>
                            <vc:single-date-time-picker
                                date-info="@(typeof(DateTime).GetProperty("Date"))"
                                data="@(new { Date = Model?.DeclarationDate ?? DateTime.Now })"
                                is-create="false"
                                is-custom="true"
                                key="createdAt"
                                custom-attribute="@(new Mio.Core.SingleDateAttribute
                                                  {
                                                      DisplayName = "Ngày nhập hàng",
                                                      IsRequired = true,
                                                      DateId = "createdAt",
                                                      DatePickerId = "createdAtPicker",
                                                      CustomClass = "w-full",
                                                      FormPosition = 0
                                                  })"/>
                        </div>
                        <vc:form-select
                            id="channelId"
                            name="channelId"
                            label-text="Kênh phân phối"
                            required="true"
                            value="@(shopTypeId?.ToString() ?? "")"
                        />
                        <vc:form-select
                            id="areaId"
                            name="areaId"
                            label-text="Khu vực phân phối"
                            required="true"
                            value="@(regionId?.ToString() ?? "")"
                        />
                        <vc:form-select
                            id="distributorId"
                            name="distributorId"
                            label-text="Nhà phân phối"
                            required="true"
                            value="@(customerId?.ToString() ?? "")"
                        />
                    </div>
                </div>
                <div class="flex gap-2 justify-end items-center pt-4 pb-8 mx-8 mt-4 border-top border-grey-200">
                    <vc:button
                        id="modal-sell-in-cancel"
                        type="button"
                        size="lg"
                        variant="secondary"
                        text="Huỷ"
                        html-attributes="closeAtt">
                    </vc:button>
                    <vc:button
                        id="modal-sell-in-confirm"
                        type="submit"
                        size="lg"
                        text="Lưu thay đổi">
                    </vc:button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include Submit Documents Success Modal -->
@await Html.PartialAsync("Modals/_SubmitDocumentsSuccessModal")

@section Scripts {


    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Button event handlers
            const sellInId = '@Model?.SellInId';

            // Helper function to handle AJAX requests
            function handleAjaxRequest(url, data, successMessage) {
                return $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (result) {
                        if (result.success) {
                            showNotification('success', successMessage || result.message);
                            setTimeout(() => {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showNotification('error', result.message || 'Thao tác thất bại!');
                        }
                    },
                    error: function () {
                        showNotification('error', 'Có lỗi xảy ra khi thực hiện thao tác!');
                    }
                });
            }

            // Revoke Import Button
            const btnRevoke = document.getElementById('btnRevoke');
            if (btnRevoke) {
                btnRevoke.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    if (!confirm('Bạn có chắc chắn muốn thu hồi nhập hàng này?')) return;

                    // Set the warranty ID for the modal to use
                    window.duplicateMachineSellInId = @Model.SellInId;

                    handleAjaxRequest(
                        '@Url.Action("RevokeImport", "ProductImport", new { area = "EU" })',
                        {id: sellInId},
                        'Thu hồi nhập hàng thành công!'
                    );
                });
            }

            // Pending Cancel Button
            const btnPendingCancel = document.getElementById('btnPendingCancel');
            if (btnPendingCancel) {
                btnPendingCancel.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }

                    // Navigate to CancelSellIn page with SellInId as route parameter
                    window.location.href = '@Url.Action("CancelSellIn", "ProductImport", new { area = "EU" })' + '/' + sellInId;
                });
            }

            // View Reward Button
            const btnViewReward = document.getElementById('btnViewReward');
            if (btnViewReward) {
                btnViewReward.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    // Navigate to the ViewReward page with SellInId as route parameter
                    window.location.href = '@Url.Action("ViewReward", "ProductImport", new { area = "EU" })' + '/' + sellInId;
                });
            }

            // Reject Button
            const btnReject = document.getElementById('btnReject');
            if (btnReject) {
                btnReject.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    if (!confirm('Bạn có chắc chắn muốn từ chối nhập hàng này?')) return;

                    handleAjaxRequest(
                        '@Url.Action("RejectSellIn", "ProductImport", new { area = "EU" })',
                        {sellInId: sellInId},
                        'Từ chối nhập hàng thành công!'
                    );
                });
            }

            // Approve Button
            const btnApprove = document.getElementById('btnApprove');
            if (btnApprove) {
                btnApprove.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    if (!confirm('Bạn có chắc chắn muốn duyệt nhập hàng này?')) return;

                    handleAjaxRequest(
                        '@Url.Action("ApproveSellIn", "ProductImport", new { area = "EU" })',
                        {sellInId: sellInId},
                        'Duyệt nhập hàng thành công!'
                    );
                });
            }

            // Reject Cancel Button
            const btnRejectCancel = document.getElementById('btnRejectCancel');
            if (btnRejectCancel) {
                btnRejectCancel.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    if (!confirm('Bạn có chắc chắn muốn từ chối yêu cầu hủy nhập hàng này?')) return;

                    handleAjaxRequest(
                        '@Url.Action("RejectCancel", "ProductImport", new { area = "EU" })',
                        {sellInId: sellInId},
                        'Từ chối hủy nhập hàng thành công!'
                    );
                });
            }

            // Approve Cancel Button
            const btnApproveCancel = document.getElementById('btnApproveCancel');
            if (btnApproveCancel) {
                btnApproveCancel.addEventListener('click', function () {
                    if (!sellInId) {
                        showNotification('error', 'Không tìm thấy thông tin nhập hàng!');
                        return;
                    }
                    if (!confirm('Bạn có chắc chắn muốn duyệt yêu cầu hủy nhập hàng này?')) return;

                    handleAjaxRequest(
                        '@Url.Action("ApproveCancel", "ProductImport", new { area = "EU" })',
                        {sellInId: sellInId},
                        'Duyệt hủy nhập hàng thành công!'
                    );
                });
            }
            const sellInEditBtn = document.getElementById('sell-in-edit-btn');
            var $sellInEditModal = $('#modal-sell-in-edit');
            var $sellInEditForm = $('#sell-in-edit-form');
            var $channelSelect = $('#channelId');
            var $areaSelect = $('#areaId');
            var $distributorSelect = $('#distributorId');

            if (sellInEditBtn) {
                sellInEditBtn.addEventListener('click', function () {
                    $sellInEditModal.modal('show');
                });
            }

            // Fetch channel options
            function fetchChannels() {
                $.ajax({
                    url: '@Url.Action("GetChannelSelectList")',
                    method: 'GET',
                    success: function ({data}) {
                        $channelSelect.empty();
                        $channelSelect.append('<option value="">Chọn kênh phân phối</option>');
                        data.forEach(function (item) {
                            $channelSelect.append($('<option></option>').attr('value', item.value).text(item.text));
                        });
                    }
                });
            }

            // Fetch area options based on channel
            function fetchAreas() {
                $.ajax({
                    url: '@Url.Action("GetAreaSelectList")',
                    method: 'GET',
                    success: function ({data}) {
                        $areaSelect.empty();
                        $areaSelect.append('<option value="">Chọn khu vực phân phối</option>');
                        data.forEach(function (item) {
                            $areaSelect.append($('<option></option>').attr('value', item.value).text(item.text));
                        });
                    }
                });
            }

            // Fetch distributor options based on channel and area
            function fetchDistributors(channelId, areaId) {
                console.log('Fetching distributors for channelId:', channelId, 'areaId:', areaId);
                if (channelId && areaId) {
                    return $.ajax({
                        url: '@Url.Action("GetDistributorSelectList")',
                        method: 'GET',
                        data: {channelId: channelId, areaId: areaId},
                        success: function ({data}) {
                            console.log('Distributor data received:', data);
                            // Update the form-select with new options
                            if ($distributorSelect.length > 0) {
                                // Clear existing options
                                $distributorSelect.empty();
                                $distributorSelect.append('<option value="">Chọn nhà phân phối</option>');

                                // Add new options
                                data.forEach(function (item) {
                                    $distributorSelect.append($('<option></option>').attr('value', item.value).text(item.text));
                                    console.log('Added distributor option:', item.text);
                                });

                                // Enable the select
                                $distributorSelect.prop('disabled', false);

                                console.log('Distributor select enabled with', data.length, 'options');
                            } else {
                                console.log('Distributor select element not found');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching distributors:', error);
                            console.error('Response:', xhr.responseText);
                        }
                    });
                } else {
                    console.log('Channel or area not selected, disabling distributor select');
                    // Disable the select and clear options
                    if ($distributorSelect.length > 0) {
                        $distributorSelect.prop('disabled', true);
                        $distributorSelect.empty();
                        $distributorSelect.append('<option value="">Chọn nhà phân phối</option>');
                    }
                    return $.Deferred().resolve().promise();
                }
            }


            // Initial fetch
            fetchChannels();
            fetchAreas();
            // Initialize distributor select as disabled
            if ($distributorSelect.length > 0) {
                $distributorSelect.prop('disabled', true);
                $distributorSelect.empty();
                $distributorSelect.append('<option value="">Chọn nhà phân phối</option>');
            }

            // Initialize form values when modal is shown
            $sellInEditModal.on('shown.bs.modal', function () {
                // Set initial values after options are loaded
                setTimeout(function () {
                    // Set channel value
                    if ('@shopTypeId' && '@shopTypeId' !== '') {
                        $channelSelect.val('@shopTypeId');
                        console.log('Set channel value:', '@shopTypeId');
                    }

                    // Set area value
                    if ('@regionId' && '@regionId' !== '') {
                        $areaSelect.val('@regionId');
                        console.log('Set area value:', '@regionId');
                    }

                    // Set distributor value and trigger change to load dependent options
                    if ('@customerId' && '@customerId' !== '') {
                        console.log('Setting up distributor selection for customerId:', '@customerId');

                        // First trigger the change events to load dependent options
                        $channelSelect.trigger('change');
                        $areaSelect.trigger('change');

                        // Use promises to ensure distributor options are loaded before setting value
                        const channelId = '@shopTypeId';
                        const areaId = '@regionId';

                        if (channelId && areaId) {
                            fetchDistributors(channelId, areaId).then(function () {
                                // Set the distributor value after options are loaded
                                setTimeout(function () {
                                    $distributorSelect.val('@customerId');
                                    console.log('Set distributor value:', '@customerId');
                                }, 300);
                            });
                        }
                    }

                    // Fix date picker z-index issues in modal
                    const datePickerInput = document.getElementById('createdAtPicker');
                    if (datePickerInput) {
                        const calendarContainer = datePickerInput.parentElement.querySelector('.calendar-container');
                        if (calendarContainer) {
                            calendarContainer.style.zIndex = '9999';
                            console.log('Fixed date picker z-index in modal');
                        }
                    }
                }, 200); // Increased initial delay
            });

            function updateDistributorSelectState() {
                var channelId = $channelSelect.val();
                var areaId = $areaSelect.val();
                if (channelId && areaId) {
                    fetchDistributors(channelId, areaId);
                }
            }

            $channelSelect.on('change', function () {
                // Disable distributor select and clear selection
                if ($distributorSelect.length > 0) {
                    $distributorSelect.prop('disabled', true);
                    $distributorSelect.val('');
                    $distributorSelect.empty();
                    $distributorSelect.append('<option value="">Chọn nhà phân phối</option>');
                }
                updateDistributorSelectState();
            });
            $areaSelect.on('change', function () {
                // Disable distributor select and clear selection
                if ($distributorSelect.length > 0) {
                    $distributorSelect.prop('disabled', true);
                    $distributorSelect.val('');
                    $distributorSelect.empty();
                    $distributorSelect.append('<option value="">Chọn nhà phân phối</option>');
                }
                updateDistributorSelectState();
            });


            // Form validation and submit
            $sellInEditForm.validate({
                rules: {
                    createdAt: {required: true},
                    channelId: {required: true},
                    areaId: {required: true},
                    distributorId: {required: true}
                },
                messages: {
                    createdAt: {required: 'Vui lòng chọn ngày nhập hàng'},
                    channelId: {required: 'Vui lòng chọn kênh phân phối'},
                    areaId: {required: 'Vui lòng chọn khu vực phân phối'},
                    distributorId: {required: 'Vui lòng chọn nhà phân phối'}
                },
                submitHandler: function (form, event) {
                    event.preventDefault();
                    var $submitButton = $('#modal-sell-in-confirm');
                    $submitButton.prop('disabled', true);
                    var formData = $sellInEditForm.serialize();
                    $.ajax({
                        url: '@Url.Action("UpdateSellInInfo")',
                        type: 'POST',
                        data: formData,
                        success: function (response) {
                            $submitButton.prop('disabled', false);
                            if (response.success) {
                                $sellInEditModal.modal('hide');
                                window.location.reload();
                            } else {
                                alert(response.message || 'Cập nhật thất bại!');
                            }
                        },
                        error: function () {
                            $submitButton.prop('disabled', false);
                            alert('Có lỗi xảy ra khi cập nhật!');
                        }
                    });
                },
                errorPlacement: function (error, element) {
                    element.parent().parent().children().last().append(error);
                },
                errorClass: 'invalid',
                errorElement: 'div',
            });

            // File upload tracking for SellIn documents - following WarrantyDetail pattern
            let uploadedFiles = {
                invoice: [],
                barcode: []
            };

            let submitDocumentsBtn = document.getElementById('submitDocumentsBtn');
            let submitBtnSpinner = document.getElementById('submitBtnSpinner');
            let submitBtnText = document.getElementById('submitBtnText');

            // Helper function to check existing files count by type
            function checkExistingFiles() {
                console.log('=== CHECKING EXISTING FILES FOR SELLIN ===');

                const existingFiles = {
                    invoice: 0,
                    barcode: 0
                };

                // Check each upload component for existing files
                const uploadComponents = [
                    {id: 'invoiceFileUpload', type: 'invoice'},
                    {id: 'barcodeFileUpload', type: 'barcode'}
                ];

                uploadComponents.forEach(component => {
                    const uploadElement = document.getElementById(component.id);
                    if (uploadElement) {
                        console.log(`[${component.id}] Checking existing files...`);

                        let totalFilesForType = 0;

                        // Check if the component has initial files (existing files from backend)
                        const initialFilesAttribute = uploadElement.getAttribute('initial-files');
                        if (initialFilesAttribute) {
                            try {
                                const initialFiles = JSON.parse(initialFilesAttribute);
                                totalFilesForType += initialFiles.length;
                                console.log(`[${component.id}] Initial files from backend: ${initialFiles.length}`);
                            } catch (e) {
                                console.log(`[${component.id}] No valid initial files from backend`);
                            }
                        }

                        // Check for uploaded files in the container (newly uploaded files)
                        const container = uploadElement.closest('.image-upload-container');
                        if (container) {
                            console.log(`[${component.id}] Container found, checking uploadedFiles...`);

                            if (container.uploadedFiles && Array.isArray(container.uploadedFiles)) {
                                console.log(`[${component.id}] container.uploadedFiles array found with ${container.uploadedFiles.length} items`);

                                let containerFileCount = 0;
                                container.uploadedFiles.forEach((item, index) => {
                                    if (item instanceof File) {
                                        containerFileCount++;
                                        console.log(`[${component.id}] File object at index ${index}: ${item.name} (${item.size} bytes)`);
                                    } else if (item && (item.fileName || item.name || item.FileName)) {
                                        containerFileCount++;
                                        console.log(`[${component.id}] File data at index ${index}: ${item.fileName || item.name || item.FileName}`);
                                    } else {
                                        console.log(`[${component.id}] Unknown item at index ${index}:`, item);
                                    }
                                });

                                totalFilesForType += containerFileCount;
                                console.log(`[${component.id}] Total files in container: ${containerFileCount}`);
                            } else {
                                console.log(`[${component.id}] No uploadedFiles array found in container`);
                            }
                        } else {
                            console.log(`[${component.id}] No container found`);
                        }

                        // Check for files in the element's uploadedFiles property (fallback)
                        if (uploadElement.uploadedFiles && Array.isArray(uploadElement.uploadedFiles) && uploadElement.uploadedFiles.length > 0) {
                            if (!container || !container.uploadedFiles || container.uploadedFiles.length === 0) {
                                totalFilesForType += uploadElement.uploadedFiles.length;
                                console.log(`[${component.id}] Files in element.uploadedFiles: ${uploadElement.uploadedFiles.length}`);
                            }
                        }

                        // Check file input as additional verification
                        const fileInput = uploadElement;
                        if (fileInput && fileInput.files && fileInput.files.length > 0) {
                            console.log(`[${component.id}] File input has ${fileInput.files.length} files selected`);
                            if (totalFilesForType === 0) {
                                totalFilesForType += fileInput.files.length;
                                console.log(`[${component.id}] Using file input count as fallback: ${fileInput.files.length}`);
                            }
                        }

                        existingFiles[component.type] = totalFilesForType;
                        console.log(`[${component.id}] Total files for ${component.type}: ${totalFilesForType}`);
                    } else {
                        console.log(`[${component.id}] Upload element not found`);
                    }
                });

                console.log('Total existing files by type:', existingFiles);
                return existingFiles;
            }

            // Helper function to collect new file uploads from components
            function collectNewFileUploads() {
                console.log('=== COLLECTING NEW FILE UPLOADS FOR SELLIN ===');

                const newFiles = [];

                // Check each upload component for new files
                const uploadComponents = [
                    {id: 'invoiceFileUpload', name: 'invoiceFile', type: 'invoice'},
                    {id: 'barcodeFileUpload', name: 'barcodeFile', type: 'barcode'}
                ];

                uploadComponents.forEach(component => {
                    const uploadElement = document.getElementById(component.id);
                    if (uploadElement) {
                        console.log(`[${component.id}] Checking for files...`);

                        const filesFromThisComponent = [];

                        // Primary source: Check for files stored in the container
                        const container = uploadElement.closest('.image-upload-container');
                        if (container && container.uploadedFiles && container.uploadedFiles.length > 0) {
                            console.log(`[${component.id}] Found ${container.uploadedFiles.length} files in container.uploadedFiles`);
                            container.uploadedFiles.forEach((file, index) => {
                                if (file instanceof File) {
                                    filesFromThisComponent.push(file);
                                    console.log(`[${component.id}] Found File object from container: ${file.name} (${file.size} bytes)`);
                                } else {
                                    console.log(`[${component.id}] Skipping non-File object at index ${index}:`, typeof file);
                                }
                            });
                        } else {
                            console.log(`[${component.id}] No files found in container.uploadedFiles`);
                        }

                        // Secondary source: Check for files in the file input
                        const fileInput = uploadElement;
                        if (fileInput && fileInput.files && fileInput.files.length > 0) {
                            console.log(`[${component.id}] Found ${fileInput.files.length} files in input.files`);
                            for (let i = 0; i < fileInput.files.length; i++) {
                                const file = fileInput.files[i];
                                const isDuplicate = filesFromThisComponent.some(f => f.name === file.name && f.size === file.size);
                                if (!isDuplicate) {
                                    filesFromThisComponent.push(file);
                                    console.log(`[${component.id}] Found File object from input: ${file.name} (${file.size} bytes)`);
                                } else {
                                    console.log(`[${component.id}] Skipping duplicate file from input: ${file.name}`);
                                }
                            }
                        } else {
                            console.log(`[${component.id}] No files found in input.files`);
                        }

                        // Add all collected files from this component to the final list
                        filesFromThisComponent.forEach(file => {
                            newFiles.push({
                                fieldName: component.name,
                                file: file,
                                type: component.type
                            });
                            console.log(`[${component.id}] Added to final list: ${file.name} (${file.size} bytes)`);
                        });

                        console.log(`[${component.id}] Total files collected: ${filesFromThisComponent.length}`);
                    } else {
                        console.log(`[${component.id}] Upload element not found`);
                    }
                });

                console.log(`Total new files collected across all components: ${newFiles.length}`);
                return newFiles;
            }

            // Function to check if all required files are uploaded
            function checkRequiredFiles() {
                const existingFiles = checkExistingFiles();
                const hasInvoice = existingFiles.invoice > 0;
                const hasBarcode = existingFiles.barcode > 0;

                if (submitDocumentsBtn) {
                    submitDocumentsBtn.disabled = !(hasInvoice && hasBarcode);
                    if (submitDocumentsBtn.disabled) {
                        submitDocumentsBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    } else {
                        submitDocumentsBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                    }
                }
            }

            // Function to submit all documents - following WarrantyDetail pattern
            async function submitSellInDocuments() {
                console.log('=== SUBMIT SELLIN DOCUMENTS ===');

                if (!submitDocumentsBtn || submitDocumentsBtn.disabled) {
                    console.log('Submit button is disabled or not found');
                    return;
                }

                try {
                    // Enhanced loading state with spinner
                    submitDocumentsBtn.disabled = true;
                    if (submitBtnSpinner && submitBtnText) {
                        submitBtnSpinner.classList.remove('hidden');
                        submitBtnText.textContent = 'Đang gửi...';
                    } else {
                        submitDocumentsBtn.textContent = 'Đang gửi...';
                    }

                    // Check existing files and collect new uploads
                    const existingFiles = checkExistingFiles();
                    const newFiles = collectNewFileUploads();

                    console.log('Existing files found:', existingFiles);
                    console.log('New files to upload:', newFiles);

                    // Create form data for submission
                    const formData = new FormData();
                    let totalFileCount = 0;

                    // Add new uploaded files to form data
                    newFiles.forEach(fileInfo => {
                        formData.append(fileInfo.fieldName, fileInfo.file);
                        totalFileCount++;
                        console.log(`Added new file: ${fileInfo.fieldName} = ${fileInfo.file.name}`);
                    });

                    // Check if we have files (either existing or new)
                    const hasExistingFiles = existingFiles.invoice || existingFiles.barcode;
                    const hasNewFiles = totalFileCount > 0;

                    console.log('=== FILE VALIDATION ===');
                    console.log('Has existing files:', hasExistingFiles);
                    console.log('Has new files:', hasNewFiles);
                    console.log('Total file count:', totalFileCount);
                    console.log('Existing files breakdown:', existingFiles);

                    if (!hasExistingFiles && !hasNewFiles) {
                        console.log('ERROR: No files detected for submission');
                        showNotification('error', 'Vui lòng tải lên ít nhất một file trước khi gửi chứng từ');

                        // Reset button state before returning
                        submitDocumentsBtn.disabled = false;
                        if (submitBtnSpinner && submitBtnText) {
                            submitBtnSpinner.classList.add('hidden');
                            submitBtnText.textContent = 'Gửi chứng từ';
                        } else {
                            submitDocumentsBtn.textContent = 'Gửi chứng từ';
                        }
                        return;
                    }

                    const hasInvoice = existingFiles.invoice || newFiles.some(f => f.type === 'invoice');
                    const hasBarcode = existingFiles.barcode || newFiles.some(f => f.type === 'barcode');

                    if (!hasInvoice || !hasBarcode) {
                        console.log('ERROR: Invoice and Barcode files are required');
                        showNotification('error', 'Vui lòng tải lên cả hóa đơn (invoice) và mã vạch (barcode) trước khi gửi');

                        // Reset button state
                        submitDocumentsBtn.disabled = false;
                        if (submitBtnSpinner && submitBtnText) {
                            submitBtnSpinner.classList.add('hidden');
                            submitBtnText.textContent = 'Gửi chứng từ';
                        } else {
                            submitDocumentsBtn.textContent = 'Gửi chứng từ';
                        }
                        return;
                    }

                    // Add SellInId to form data
                    formData.append('sellInId', '@Model?.SellInId');

                    // Submit to backend
                    console.log('=== SUBMITTING TO BACKEND ===');
                    console.log('Form data entries:');
                    for (let [key, value] of formData.entries()) {
                        console.log(`${key}:`, value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value);
                    }

                    const response = await fetch('@Url.Action("SubmitSellInDocuments", "ProductImport", new { area = "EU" })', {
                        method: 'POST',
                        body: formData
                    });

                    console.log('Response status:', response.status);
                    console.log('Response ok:', response.ok);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('HTTP error response:', errorText);
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }

                    const result = await response.json();
                    console.log('=== BACKEND RESPONSE ===');
                    console.log('Full result:', result);

                    if (result.success) {
                        console.log('✅ SUCCESS RESPONSE');
                        showNotification('success', result.message || 'Gửi chứng từ thành công');

                        // Clear upload components
                        const clearComponents = [
                            '.warranty-invoice-upload',
                            '.warranty-barcode-upload'
                        ];

                        clearComponents.forEach(selector => {
                            const uploadContainer = document.querySelector(selector);
                            if (uploadContainer) {
                                console.log(`Clearing files for container: ${selector}`);

                                // Clear stored files
                                uploadContainer.uploadedFiles = [];

                                // Clear native file input
                                const fileInput = uploadContainer.querySelector('input[type="file"]');
                                if (fileInput) {
                                    fileInput.value = '';
                                }

                                // Hide preview and show dropzone
                                const previewContainer = uploadContainer.querySelector('.image-preview-container');
                                const dropzone = uploadContainer.querySelector('.image-upload-dropzone');

                                if (previewContainer) {
                                    previewContainer.style.display = 'none';
                                }
                                if (dropzone) {
                                    dropzone.style.display = 'flex';
                                }
                            }
                        });

                        // Show success modal instead of reloading
                        const successModal = new bootstrap.Modal(document.getElementById('sellInSubmitDocumentsSuccessModal'));
                        successModal.show();
                    } else {
                        showNotification('error', result.message || 'Có lỗi xảy ra khi gửi chứng từ');

                        // Show detailed errors if available
                        if (result.errors && result.errors.length > 0) {
                            result.errors.forEach(error => {
                                console.error('Submit error:', error);
                            });
                        }
                    }
                } catch (error) {
                    console.error('Submit documents error:', error);
                    showNotification('error', 'Có lỗi xảy ra khi gửi chứng từ: ' + error.message);
                } finally {
                    // Reset button state
                    submitDocumentsBtn.disabled = false;
                    if (submitBtnSpinner && submitBtnText) {
                        submitBtnSpinner.classList.add('hidden');
                        submitBtnText.textContent = 'Gửi chứng từ';
                    } else {
                        submitDocumentsBtn.textContent = 'Gửi chứng từ';
                    }
                    checkRequiredFiles();
                }
            }

            // Event listener for submit documents button
            if (submitDocumentsBtn) {
                submitDocumentsBtn.addEventListener('click', submitSellInDocuments);
            }

            // Listen for file upload events from image-upload components
            document.addEventListener('imageUploaded', function (event) {
                const {name, files} = event.detail;

                if (name === 'invoiceFile' && files.length > 0) {
                    console.log('Invoice file uploaded:', files[0].name);
                    checkRequiredFiles();
                } else if (name === 'barcodeFile' && files.length > 0) {
                    console.log('Barcode file uploaded:', files[0].name);
                    checkRequiredFiles();
                }
            });

            // Listen for file removal events
            document.addEventListener('fileRemoved', function (event) {
                const {name, fileId} = event.detail;

                if (name === 'invoiceFile') {
                    console.log('Invoice file removed:', fileId);
                } else if (name === 'barcodeFile') {
                    console.log('Barcode file removed:', fileId);
                }

                checkRequiredFiles();
            });

            // Initialize required files check
            checkRequiredFiles();

            // Notification function
            function showNotification(type, message) {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
                    type === 'success' ? 'bg-green-500 text-white' :
                        type === 'error' ? 'bg-red-500 text-white' :
                            type === 'warning' ? 'bg-yellow-500 text-black' :
                                'bg-blue-500 text-white'
                }`;
                notification.textContent = message;

                // Add to page
                document.body.appendChild(notification);

                // Remove after 3 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            }

            // Additional modal-specific event handlers for date picker
            $sellInEditModal.on('shown.bs.modal', function () {
                // Ensure date picker dropdown has correct z-index when modal is shown
                const datePickerInput = document.getElementById('createdAtPicker');
                if (datePickerInput) {
                    const calendarContainer = datePickerInput.parentElement.querySelector('.calendar-container');
                    if (calendarContainer) {
                        calendarContainer.style.zIndex = '9999';
                        console.log('Modal shown - fixed date picker z-index');
                    }
                }
            });

            // Handle modal scroll events for date picker
            $sellInEditModal.on('scroll', function () {
                const datePickerInput = document.getElementById('createdAtPicker');
                if (datePickerInput) {
                    const calendarContainer = datePickerInput.parentElement.querySelector('.calendar-container');
                    if (calendarContainer && calendarContainer.style.display === 'block') {
                        // Trigger repositioning of the date picker dropdown
                        setTimeout(() => {
                            const event = new Event('click');
                            datePickerInput.dispatchEvent(event);
                        }, 100);
                    }
                }
            });
        });
    </script>
}