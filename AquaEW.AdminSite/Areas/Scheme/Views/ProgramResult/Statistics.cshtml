@using AquaEW.Core.EnumModel
@using AquaEW.Core.ViewModel
@using AquaEW.Core.ViewModel.EW
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Mio.Core
@using Mio.Core.ViewModel

@{
    ViewData["Title"] = "Kết quả theo cửa hàng";
    ViewData[SystemConst.VIEW_DATATABLE] = true;
    var currentTab = "Statistics";
    var tabs = new List<TabViewModel>
   {
        new TabViewModel
        {
            Id = "program-result-tab",
            Label = "Kết quả chương trình",
            IsActive = false,
            Url = Url.Action("Result", "ProgramResult")
        },
        new TabViewModel
        {
            Id = "statistics-tab",
            Label = "Kết quả theo CH",
            IsActive = true,
            Url = Url.Action("Statistics", "ProgramResult")
        }
    };
}

@section Title{
    <div class="w-full border-b">
        <div class="!px-8 !pt-6 !mb-4">
            <h1 class="text-2xl font-semibold">@ViewData["Title"]</h1>
        </div>
    </div>
    <div class="flex border-b">
        <vc:admin-tab tabs="tabs" id="tab-list"></vc:admin-tab>
    </div>
}

<div class="">
    <div class="flex 2xl:gap-2 justify-between align-items-center grid grid-cols-1 2xl:grid-cols-6">
        <div class="flex xl:gap-2 col-span-4 grid grid-cols-1 xl:grid-cols-4">
            <div class="flex gap-2">
                <div class="col-span-2 flex items-end gap-2">
                    <div class="flex flex-col w-[140px]">
                        <label for="statistics-filter-month" class="block mb-1.5 text-sm font-medium text-grey-700">
                            Chọn tháng
                        </label>
                        <select id="statistics-filter-month" class="form-control select2 !mb-0 min-w-[120px]">
                            <option value="">-- Chọn tháng --</option>
                            @for (int m = 1; m <= 12; m++) {
                                if (m == DateTime.Now.Month) {
                                    <option value="@m" selected>Tháng @m</option>
                                } else {
                                    <option value="@m">Tháng @m</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="flex flex-col w-[140px]">
                        <label for="statistics-filter-year" class="block mb-1.5 text-sm font-medium text-grey-700">
                            Chọn năm
                        </label>
                        <select id="statistics-filter-year" class="form-control select2 !mb-0 min-w-[120px]">
                            <option value="">-- Chọn năm --</option>
                            @for (int y = DateTime.Now.Year - 5; y <= DateTime.Now.Year + 1; y++) {
                                if (y == DateTime.Now.Year) {
                                    <option value="@y" selected>@y</option>
                                } else {
                                    <option value="@y">@y</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                @* <div class="col-span-2">
                    <label for="" class="block mb-1.5 text-sm font-normal text-grey-700">
                        Loại chương trình
                    </label>
                    <select id="statistics-filter-program-type" class="select2-ajax"
                            data-ajax-url="@Url.Action("GetAllProgramTypesAsSelect2")">
                        <option value="">- Chọn loại chương trình -</option>
                    </select>
                </div>
                <vc:input label="Mã chương trình" placeholder="Mã chương trình" id="filter-program-code" css-class="!mb-0"></vc:input> *@
            </div>
            <div
                class="col-span-1 flex gap-2 sm:mt-2 xl:mb-auto xl:mt-auto 2xl:mb-0 2xl:mt-auto grid grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2">
                <vc:button text="Tìm kiếm" variant="primary" size="sm" id="btn-statistics-filter"
                           css-class="h-[40px] !justify-center"></vc:button>
                <vc:button icon-left="✕" text="Bỏ lọc"
                           variant="outlined-secondary" size="sm"
                           id="btn-statistics-clear-filter" css-class="h-[40px] mt-auto xl:mt-auto !justify-center"></vc:button>
            </div>
        </div>
        <div class="col-span-2 flex gap-2 2xl:mt-auto 2xl:mb-0 sm:mt-2 ms-auto w-100 items-center justify-end">
            <div class="flex gap-2">
                <vc:button variant="outlined-secondary" icon-left="<img src='/images/icons/download-01.svg'>"
                    text="Xuất Excel" id="btn-export-statistics" css-class="h-[40px]"></vc:button>
                <vc:button text="Hoàn tất và Gửi" size="md" id="btn-complete-send"
                    icon-left="<img src='/images/icons/check-circle.svg'>" css-class="h-[40px] bg-blue-600 text-white"></vc:button>
            </div>
        </div>
    </div>
    <div>
        <div id="statistics-tbl" class="mt-4"></div>
        <div id="statistics-detail" class="mt-4" style="display: none;"></div>
    </div>
</div>

@section Styles {
    <style>
        .dataTables_processing i {
            border: none !important;
        }

        /* Ensure proper table container height */
        #statistics-tbl {
            min-height: 500px;
        }

        /* Style pagination properly */
        .dataTables_paginate {
            margin-top: 15px !important;
        }

        .dataTables_info {
            margin-top: 15px !important;
        }

        /* Ensure pagination controls are visible */
        .dataTables_wrapper .dataTables_paginate {
            display: block !important;
        }

        /* Fix pagination layout */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }

        .col-sm-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }

        .col-md-5 {
            flex: 0 0 41.666667%;
            max-width: 41.666667%;
        }

        .col-md-7 {
            flex: 0 0 58.333333%;
            max-width: 58.333333%;
        }

        /* Media query for responsive design */
        @@media (min-width: 768px) {
            .col-md-5, .col-md-7 {
                padding-right: 15px;
                padding-left: 15px;
            }
        }
    </style>
}

@section Scripts {
    <partial name="_ValidationScriptsPartialv2"></partial>

    <script>
        const renderStatus = (data, row) => {
            // Use EnumProgram.ProgramShopStatus enum names for clarity
            switch (data) {
                case 1: // RewardApproved
                    return renderStatusBadge({ status: "success", size: "sm", text: "Đã duyệt thưởng" });
                case 2: // ResultConfirmed
                    return renderStatusBadge({ status: "success", size: "sm", text: "Xác nhận kết quả" });
                case 3: // ResultRejected
                    return renderStatusBadge({ status: "failed", size: "sm", text: "Từ chối kết quả" });
                case 4: // PendingPayment
                    return renderStatusBadge({ status: "pending", size: "sm", text: "Chờ thanh toán" });
                case 5: // Paid
                    return renderStatusBadge({ status: "success", size: "sm", text: "Đã thanh toán" });
                default:
                    return renderStatusBadge({ status: "secondary", size: "sm", text: "Không xác định" });
            }
        }

        // Currency formatting function for DatatableUtil
        const renderCurrency = (data, row) => {
            if (data === null || data === undefined || data === '') {
                return '0';
            }

            const numValue = typeof data === 'string' ? parseFloat(data) : data;

            if (isNaN(numValue)) {
                return '0';
            }

            // Keep the negative sign
            return new Intl.NumberFormat('vi-VN').format(numValue);
        }

        // Percentage formatting function for DatatableUtil
        const renderPercentage = (data, row) => {
            if (data === null || data === undefined || data === '') {
                return '0%';
            }

            // Convert to number if it's a string
            const numValue = typeof data === 'string' ? parseFloat(data) : data;

            if (isNaN(numValue)) {
                return '0%';
            }

            // Format as percentage with 2 decimal places
            return (numValue * 100).toFixed(2) + '%';
        }

        // Payment status formatting function for DatatableUtil
        const renderPaymentStatus = (data, row) => {
            if (!data) {
                return '<span class="badge badge-secondary">Chưa thanh toán</span>';
            }

            switch (data.toLowerCase()) {
                case 'paid':
                case 'đã thanh toán':
                    return '<span class="badge badge-success">Đã thanh toán</span>';
                case 'pending':
                case 'đang chờ':
                    return '<span class="badge badge-warning">Đang chờ</span>';
                case 'failed':
                case 'thất bại':
                    return '<span class="badge badge-danger">Thất bại</span>';
                default:
                    return '<span class="badge badge-secondary">' + data + '</span>';
            }
        }

        const renderButton = (data, row) => {
            // By default, don't show the send button unless it's explicitly needed for resend
            // You can add a condition here to show the button for specific cases
            return `<button class="btn bg-blue-600 text-white btn-sm btn-reset-send"
                        data-shop-id="${row.shopId || ''}"
                        data-program-code="${row.programCode || ''}"
                        data-period="${row.period || ''}"
                        title="Click to enable resend">
                        Gửi
                    </button>`;

        }

        // Helper function to get date range from month/year dropdowns
        const getDateRangeFromFilters = () => {
            const selectedMonth = $("#statistics-filter-month").val();
            const selectedYear = $("#statistics-filter-year").val();

            let startDate = null;
            let endDate = null;

            if (selectedMonth && selectedYear) {
                const year = parseInt(selectedYear);
                const month = parseInt(selectedMonth);
                startDate = new Date(year, month - 1, 1).toISOString().split('T')[0];
                const lastDay = new Date(year, month, 0).getDate();
                endDate = new Date(year, month - 1, lastDay).toISOString().split('T')[0];
            } else if (selectedYear) {
                const year = parseInt(selectedYear);
                startDate = new Date(year, 0, 1).toISOString().split('T')[0];
                endDate = new Date(year, 11, 31).toISOString().split('T')[0];
            } else if (selectedMonth) {
                const currentYear = new Date().getFullYear();
                const month = parseInt(selectedMonth);
                startDate = new Date(currentYear, month - 1, 1).toISOString().split('T')[0];
                const lastDay = new Date(currentYear, month, 0).getDate();
                endDate = new Date(currentYear, month - 1, lastDay).toISOString().split('T')[0];
            }

            return { startDate, endDate };
        };

        // Make all render functions globally available for DatatableUtil
        window.renderCurrency = renderCurrency;
        window.renderPercentage = renderPercentage;
        window.renderPaymentStatus = renderPaymentStatus;
        window.renderStatus = renderStatus;

        // Declare date picker variables globally
        let statisticsFilterPicker;
        let periodFilterPicker;
        let isShowingItem = false;
        let showingItemId = null;

        $(async () => {
            // Initialize date pickers first
            statisticsFilterPicker = initDateRangePicker("statisticsFilterPicker", "date-start-at", "date-end-at");
            periodFilterPicker = initDateRangePicker("periodFilterPicker", "period-start-at", "period-end-at");

            // --- Set default month/year and date range to current month/year ---
            const now = new Date();
            const currentMonth = now.getMonth() + 1; // JS months are 0-based
            const currentYear = now.getFullYear();
            // Set dropdowns
            $("#statistics-filter-month").val(currentMonth);
            $("#statistics-filter-year").val(currentYear);
            // Set date pickers to first and last day of current month
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            const lastDay = new Date(currentYear, currentMonth, 0); // day 0 of next month = last day of current month
            // Format as yyyy-MM-dd
            const pad = n => n.toString().padStart(2, '0');
            const firstDayStr = `${firstDay.getFullYear()}-${pad(firstDay.getMonth() + 1)}-${pad(firstDay.getDate())}`;
            const lastDayStr = `${lastDay.getFullYear()}-${pad(lastDay.getMonth() + 1)}-${pad(lastDay.getDate())}`;
            $("#date-start-at").val(firstDayStr);
            $("#date-end-at").val(lastDayStr);
            if (statisticsFilterPicker && typeof statisticsFilterPicker.setDateRange === 'function') {
                statisticsFilterPicker.setDateRange(firstDayStr, lastDayStr, true);
            }
            // Optionally, set period month/year as well
            $("#statistics-filter-period-month").val(currentMonth);
            $("#statistics-filter-period-year").val(currentYear);
            if (periodFilterPicker && typeof periodFilterPicker.setDateRange === 'function') {
                periodFilterPicker.setDateRange(firstDayStr, lastDayStr, true);
            }

            // --- End default logic ---

            // Always use DatatableUtil.init for table setup (no fallback)
            DatatableUtil.setOption({
                datatableInfoUrl: "@Url.Action("GetDatatableInfo", "Helper", new { Area = "" })"
            });

            var statisticsTable = await DatatableUtil.init($("#statistics-tbl"), {
                name: "ProgramShopStatisticsViewModel",
                id: "ProgramShopStatistics",
                url: "@Url.Action("GetStatistics")",
                customAjaxData: {
                    startDate: () => {
                        const dateRange = getDateRangeFromFilters();
                        return dateRange.startDate || $("#date-start-at").val() || "";
                    },
                    endDate: () => {
                        const dateRange = getDateRangeFromFilters();
                        return dateRange.endDate || $("#date-end-at").val() || "";
                    }
                },
                rowId: (row) => `row-${row.shopId || 'unknown'}-${row.programCode || 'unknown'}`,
                fixedColumns: {
                    left: 1,
                    right: 1
                },
                scrollCollapse: false,
                scrollX: true,
                paging: true,
                pageLength: 25,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                expandFunction: async (data, element) => {
                    // ...existing expand logic...
                    if (!data || !data.shopId || !data.programId) {
                        alert('Không thể mở chi tiết: thiếu shopId hoặc programId trong dữ liệu hàng.\n' + JSON.stringify(data));
                        return "";
                    }
                    if (showingItemId !== `${data.shopId}_${data.programId}`) {
                        $('#statistics-tbl tr.shown').removeClass('shown');
                        $('#statistics-tbl .expand-row-btn').html('&#9654;');
                        $('#statistics-detail').html('<div style="padding:24px;text-align:center;">Đang tải chi tiết...</div>').show();
                        $(element).addClass('shown');
                        await $.get(`@Url.Action("GetStatisticsDetail", "ProgramResult")?shopId=${data.shopId}&programId=${data.programId}`,
                            function(html) {
                                $('#statistics-detail').html(html).show();
                            }
                        ).fail(function() {
                            $('#statistics-detail').html('<div style="padding:24px;text-align:center;color:red;">Không thể tải chi tiết.</div>');
                        });
                        var $detail = $("#statistics-detail");
                        if ($detail.length && $detail.offset()) {
                            $('html, body').animate({ scrollTop: $detail.offset().top }, 0);
                        }
                        isShowingItem = true;
                        showingItemId = `${data.shopId}_${data.programId}`;
                    } else {
                        $('#statistics-tbl tr.shown').removeClass('shown');
                        $('#statistics-detail').hide().html("");
                        isShowingItem = false;
                        showingItemId = null;
                    }
                    return "";
                },
                onTableLoadedFunction: async (data, element) => {
                    $(`<button id="btn-export" type="button" class="flex justify-between items-center gap-2 rounded-lg font-med bg-grey-200 text-[#1A1919] focus:outline-none transition-colors bg-transparent text-blue-600 hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 text-sm px-3 py-1.5 h-[40px]">
                        <span><img src="/images/icons/download-01.svg" alt="" class="w-[16px] h-[16px]"></span>
                    </button>`)
                        .click((e) => {
                            exportExcel()
                        })
                        .insertAfter($(".dt-paging", $("#statistics-tbl")));
                }
            });
        })


        const validateDateRange = () => {
            const curStartAt = $("#date-start-at").val();
            const curEndAt = $("#date-end-at").val();
            if (curStartAt && curEndAt && Math.abs(moment(curEndAt).diff(moment(curStartAt), 'years')) > 5) {
                return false;
            }
            return true;
        }

        $("#btn-statistics-filter").click(function (e) {
            if (!validateDateRange()) {
                statisticsFilterPicker.setDateRange(null, null, true)
                showModalConfirm({
                    title: "Thời gian không hợp lệ",
                    htmlContent: "Khoảng thời gian tối đa không vượt quá 5 năm",
                    confirmClass: "hidden"
                })
                return;
            }

            // Try DatatableUtil first, fallback to regular DataTable
            if (typeof DatatableUtil !== 'undefined' && DatatableUtil.get("ProgramShopStatistics")) {
                DatatableUtil.get("ProgramShopStatistics").ajax.reload();
            } else if ($('#statisticsTable').length && $.fn.DataTable.isDataTable('#statisticsTable')) {
                $('#statisticsTable').DataTable().ajax.reload();
            }
        })

        $("#btn-statistics-clear-filter").click(function (e) {
            statisticsFilterPicker.setDateRange(null, null, true);
            periodFilterPicker.setDateRange(null, null, true);
            $("#statistics-filter-program-type").val("").trigger("change.select2");
            $("#filter-program-code").val("")

            // Try DatatableUtil first, fallback to regular DataTable
            if (typeof DatatableUtil !== 'undefined' && DatatableUtil.get("ProgramShopStatistics")) {
                DatatableUtil.get("ProgramShopStatistics").ajax.reload();
            } else if ($('#statisticsTable').length && $.fn.DataTable.isDataTable('#statisticsTable')) {
                $('#statisticsTable').DataTable().ajax.reload();
            }
        })

        const exportExcel = () => {
            try {
                // Show loading indicator
                toastr.info('Đang xuất Excel, vui lòng đợi...');



                // Get date range from month/year dropdowns
                const dateRange = getDateRangeFromFilters();
                let startDate = dateRange.startDate;
                let endDate = dateRange.endDate;

                // If no date range from dropdowns, try to get from date picker inputs as fallback
                if (!startDate && !endDate) {
                    startDate = $("#date-start-at").val() || null;
                    endDate = $("#date-end-at").val() || null;
                }

                // Prepare filter data - always allow export with current filters
                let payload = {
                    startDate: startDate,
                    endDate: endDate,
                    selectedIds: null,
                    sheetName: "KetQuaTheoCuaHang"
                };

                console.log('Export payload:', payload);

                // Create form to submit POST request for file download
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("GetExportCount", "ProgramResult", new { Area = "Scheme" })';
                form.target = '_blank';

                // Add CSRF token
                const csrfToken = $('input[name="__RequestVerificationToken"]').val();
                if (csrfToken) {
                    const tokenInput = document.createElement('input');
                    tokenInput.type = 'hidden';
                    tokenInput.name = '__RequestVerificationToken';
                    tokenInput.value = csrfToken;
                    form.appendChild(tokenInput);
                }

                // Add payload data as form fields
                Object.keys(payload).forEach(key => {
                    if (payload[key] !== null && payload[key] !== undefined && payload[key] !== '') {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = key;
                        input.value = payload[key];
                        form.appendChild(input);
                    }
                });

                // Submit form
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);

                // Show success message after a short delay
                setTimeout(() => {
                    toastr.success('Đã gửi yêu cầu xuất Excel!');
                }, 1000);

                console.log('Export request submitted successfully');

            } catch (error) {
                console.error('Error during export:', error);
                toastr.error('Có lỗi xảy ra khi xuất Excel: ' + error.message);
            }
        }

        // Complete and Send button event
        $("#btn-complete-send").on('click', function () {
            showModalConfirm({
                title: "Hoàn tất và gửi tất cả",
                htmlContent: "Bạn có muốn hoàn tất và gửi tất cả dữ liệu thống kê hiện tại không? Thao tác này sẽ đánh dấu tất cả các bản ghi là đã gửi.",
                action: () => {
                    completeAndSendAll();
                }
            });
        });

        // Function to complete and send all statistics
        function completeAndSendAll() {
            // Get selected month and year
            const month = parseInt($('#statistics-filter-month').val());
            const year = parseInt($('#statistics-filter-year').val());
            let startDate, endDate;
            if (month && year) {
                const pad = n => n.toString().padStart(2, '0');
                startDate = `${year}-${pad(month)}-01`;
                const lastDay = new Date(year, month, 0);
                endDate = `${year}-${pad(month)}-${pad(lastDay.getDate())}`;
            } else {
                // fallback to current month if not selected
                const now = new Date();
                const currentMonth = now.getMonth() + 1;
                const currentYear = now.getFullYear();
                const pad = n => n.toString().padStart(2, '0');
                startDate = `${currentYear}-${pad(currentMonth)}-01`;
                const lastDay = new Date(currentYear, currentMonth, 0);
                endDate = `${currentYear}-${pad(currentMonth)}-${pad(lastDay.getDate())}`;
            }

            callAjax({
                url: "@Url.Action("CompleteAndSendAll")",
                data: {
                    startDate: startDate,
                    endDate: endDate
                },
                convertDataToUrl: false,
                isShowMessage: false,
                isShowLoading: true,
                errorCallback: (data) => {
                    showModalConfirm({
                        title: "Có lỗi đã xảy ra",
                        htmlContent: data?.errorMessage || "Không thể hoàn tất và gửi dữ liệu thống kê.",
                        confirmClass: "hidden",
                    });
                },
                successCallback: (result) => {
                    console.log('CompleteAndSendAll result:', result);
                    // Always reload the table to reflect changes
                    if (typeof DatatableUtil !== 'undefined' && DatatableUtil.get("ProgramShopStatistics")) {
                        DatatableUtil.get("ProgramShopStatistics").ajax.reload();
                    } else if ($('#statisticsTable').length && $.fn.DataTable.isDataTable('#statisticsTable')) {
                        $('#statisticsTable').DataTable().ajax.reload();
                    }
                    if (result.success) {
                        toastr.success(result.message || "Đã hoàn tất và gửi tất cả dữ liệu thống kê!");
                        showModalConfirm({
                            title: "Thành công",
                            htmlContent: result.message || "Đã hoàn tất và gửi tất cả dữ liệu thống kê!",
                            confirmClass: "hidden",
                        });
                    } else {
                        toastr.error(result.message || result.errorMessage || "Không thể hoàn tất và gửi dữ liệu thống kê.");
                        showModalConfirm({
                            title: "Không thành công",
                            htmlContent: result.message || result.errorMessage || "Không thể hoàn tất và gửi dữ liệu thống kê.",
                            confirmClass: "hidden",
                        });
                    }
                }
            });
        }



        // Export Excel button event
        $("#btn-export-statistics").on('click', function () {
            exportExcel();
        });

        // Reset send status button event handler (for resend functionality)
        $(document).on('click', '.btn-reset-send', function() {
            const shopId = $(this).data('shop-id');
            const programCode = $(this).data('program-code');
            const period = $(this).data('period');

            showModalConfirm({
                title: "Xác nhận reset",
                htmlContent: `Bạn có chắc chắn muốn reset trạng thái gửi cho cửa hàng ${programCode} - ${period}? Sau khi reset, bạn có thể gửi lại.`,
                action: () => {
                    resetSendStatus(shopId, programCode, period, $(this));
                }
            });
        });

        // Function to reset send status
        function resetSendStatus(shopId, programCode, period, buttonElement) {
            // Disable button during processing
            buttonElement.prop('disabled', true).text('Đang xử lý...');

            callAjax({
                url: "@Url.Action("ResetSendStatus")",
                data: {
                    shopId: shopId,
                    programCode: programCode
                },
                convertDataToUrl: false,
                isShowMessage: false,
                isShowLoading: true,
                errorCallback: (data) => {
                    // Re-enable button on error
                    buttonElement.prop('disabled', false).text('Đã gửi');

                    showModalConfirm({
                        title: "Có lỗi đã xảy ra",
                        htmlContent: data?.errorMessage || "Không thể reset trạng thái gửi. Vui lòng thử lại.",
                        confirmClass: "hidden",
                    });
                },
                successCallback: (result) => {
                    if (result.success) {
                        // Reload the table to reflect changes
                        if (typeof DatatableUtil !== 'undefined' && DatatableUtil.get("ProgramShopStatistics")) {
                            DatatableUtil.get("ProgramShopStatistics").ajax.reload();
                        } else if ($('#statisticsTable').length && $.fn.DataTable.isDataTable('#statisticsTable')) {
                            $('#statisticsTable').DataTable().ajax.reload();
                        }

                        showModalConfirm({
                            title: "Thành công",
                            htmlContent: "Đã reset trạng thái gửi thành công! Bây giờ bạn có thể gửi lại.",
                            confirmClass: "hidden",
                        });
                    } else {
                        // Re-enable button on failure
                        buttonElement.prop('disabled', false).text('Đã gửi');

                        showModalConfirm({
                            title: "Reset không thành công",
                            htmlContent: result.message || "Không thể reset trạng thái gửi. Vui lòng thử lại.",
                            confirmClass: "hidden",
                        });
                    }
                }
            });
        }

        // Send statistics button event handler (for individual sends when button is shown)
        $(document).on('click', '.btn-send-statistics', function() {
            const shopId = $(this).data('shop-id');
            const programCode = $(this).data('program-code');
            const period = $(this).data('period');

            showModalConfirm({
                title: "Xác nhận gửi",
                htmlContent: `Bạn có chắc chắn muốn gửi kết quả thống kê cho cửa hàng ${programCode} - ${period}?`,
                action: () => {
                    sendStatistics(shopId, programCode, period, $(this));
                }
            });
        });

        // The expand row functionality is now handled by DatatableUtil's expandFunction
        // No need for manual click handler or expand column.
    </script>
}
