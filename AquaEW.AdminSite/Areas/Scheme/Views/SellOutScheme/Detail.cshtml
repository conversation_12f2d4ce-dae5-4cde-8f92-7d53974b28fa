@using System.IO
@using AquaEW.Core.EnumModel
@using AquaEW.Core.ViewModel
@using Mio.Core
@using Mio.Core.ViewModel
@model ProgramViewModel
@{
    ViewData["Title"] = "Thông tin chương trình";
    ViewData[SystemConst.HAS_BREADCRUMB] = true;
    ViewData[SystemConst.VIEW_DATATABLE] = true;
}

@section Title{
    <div class="w-full border-b">
        <div class="!px-8 !pt-2 !mb-4 flex justify-between">
            <div class="flex gap-2 items-center">
                <h1 class="text-2xl font-semibold">@Model.Name</h1>
                <div id="div-status"></div>
            </div>
            <div class="flex gap-2 items-center">
                @* @if (Model.ProgramStatus == EnumProgram.Status.Reject || Model.ProgramStatus == EnumProgram.Status.Draft) *@
                @* { *@
                @*     <vc:button text="<PERSON><PERSON>i <PERSON>/Publish" *@
                @*                id="btn-publish" *@
                @*                variant="primary"></vc:button> *@
                @* } *@

                <vc:button text="Chỉnh sửa"
                           id="btn-update"
                           variant="primary" html-attributes="@(new Dictionary<string, object>()
                                                              {
                                                                  { "style", "display:none" }
                                                              })"></vc:button>
            </div>
        </div>
    </div>
    <div class="flex border-b px-8">
        @{
            var tabs = new List<TabViewModel>
            {
                new TabViewModel
                {
                    Id = "tab-program",
                    Label = "Thông tin chương trình",
                    IsActive = false,
                    Url = "#content-program"
                },
                new TabViewModel
                {
                    Id = "tab-shop",
                    Label = "Thông tin cửa hàng tham gia",
                    IsActive = false,
                    Url = "#content-shop"
                },
                new TabViewModel
                {
                    Id = "tab-material",
                    Label = "Thông tin sản phẩm",
                    IsActive = false,
                    Url = "#content-material"
                },
                new TabViewModel
                {
                    Id = "tab-rule",
                    Label = "Scheme rule",
                    IsActive = false,
                    Url = "#content-rule"
                },
            };
        }
        <vc:admin-tab tabs="tabs" id="tab-list" custom-class="!px-0"></vc:admin-tab>
    </div>
}

<div class="w-full">
    <input type="hidden" id="ProgramId" value="@Model.ProgramId">
    <div>
        <div class="!p-2">
            <div id="content-program" style="display: none" class="tab-content">
                <div class="w-full" id="div-preview-program">
                    <div class="space-y-3 text-sm font-normal" style="letter-spacing: -0.15px">
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">ID CT:</div>
                            <div
                                class="w-2/3 text-gray-900">@(string.IsNullOrWhiteSpace(Model.Code) ? "-" : Model.Code)</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Tên chương trình:</div>
                            <div class="w-2/3 text-gray-900">@Model.Name</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Loại chương trình:</div>
                            <div
                                class="w-2/3 text-gray-900">@(string.IsNullOrWhiteSpace(Model.Status) ? "Normal" : "Add On")</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Từ ngày - tới ngày:</div>
                            <div
                                class="w-2/3 text-gray-900">@Model.StartEndDisplay</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Cut-off time:</div>
                            <div
                                class="w-2/3 text-gray-900">@(Model.ShopCutOffAt?.ToString("dd/MM/yyyy") ?? "-")</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Mô tả:</div>
                            <div
                                class="w-2/3 text-gray-900">@Html.Raw(Model.Description ?? "-")</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Ngày tạo:</div>
                            <div class="w-2/3 text-gray-900">@Model.CreatedAt.ToString("dd/MM/yyyy")</div>
                        </div>
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Ngày duyệt:</div>
                            <div class="w-2/3 text-gray-900">@(Model.ApproveAt?.ToString("dd/MM/yyyy") ?? "-")</div>
                        </div>
                    </div>
                </div>
                <div id="div-edit-program" class="hidden w-full">
                    <form action="@Url.Action("UpdateProgramDetail")" id="form-program">
                        <input type="hidden" id="ProgramId" name="ProgramId" value="@Model.ProgramId">
                        <partial name="Form/_FormDetailProgram" model="Model"></partial>
                        <div class="flex justify-end !gap-4">
                            <vc:button text="Hủy" variant="secondary" size="lg" type="button"
                                       id="btn-program-cancel"
                                       css-class="h-[40px]"></vc:button>
                            <vc:button text="Lưu" variant="primary" type="submit" size="lg"
                                       id="btn-program-save"
                                       css-class="h-[40px]"></vc:button>
                        </div>
                    </form>
                </div>
            </div>
            <form id="form-program-other">
                <div id="content-shop" style="display: none" class="tab-content">
                    <div class="flex justify-between">
                        <div class="flex gap-2 items-center">

                        </div>
                        <div class="flex gap-2 items-center">
                            <vc:button variant="outlined-secondary"
                                       icon-left="<img src='/images/icons/download-01.svg'>"
                                       text="Xuất Excel" id="btn-export-shop-excel"></vc:button>
                            <vc:button variant="outlined-secondary"
                                       icon-left="<img src='/images/icons/download-01.svg'>"
                                       text="Tải mẫu file Excel" id="btn-download-shop-template"></vc:button>
                            <vc:button variant="primary"
                                       icon-left="<div class='w-[24px] h-[24px] bg-white' style='-webkit-mask: url(/images/icons/upload-01.svg) no-repeat center; mask: url(/images/icons/upload-01.svg) no-repeat center;'></div>"
                                       text="Tải lên" id="btn-upload-shop-template"></vc:button>
                            <input type="file" id="file-shop" class="hidden">
                            <vc:button icon-left="+" text="Thêm CH" variant="primary" id="btn-add-shop"
                                       css-class="h-full"></vc:button>
                        </div>

                    </div>

                    <input type="hidden" name="ProgramId" value="@Model.ProgramId">
                    <div id="input-shop"></div>
                    <partial name="Form/_FormDetailShop"></partial>

                    <div class="flex justify-end !gap-4">
                        <vc:button text="Hủy" variant="secondary" size="lg" type="button"
                                   id="btn-shop-cancel"
                                   css-class="h-[40px]"></vc:button>
                        <vc:button text="Lưu" variant="primary" type="submit" size="lg"
                                   id="btn-shop-save"
                                   css-class="h-[40px]"></vc:button>
                    </div>


                </div>
                <div id="content-material" style="display: none" class="tab-content">
                    <div class="flex justify-between">
                        <div class="flex gap-2 items-center">
                        </div>
                        <div class="flex gap-2 items-center">
                            <vc:button variant="outlined-secondary"
                                       icon-left="<img src='/images/icons/download-01.svg'>"
                                       text="Xuất Excel" id="btn-export-material-excel"></vc:button>
                            <vc:button variant="outlined-secondary"
                                       icon-left="<img src='/images/icons/download-01.svg'>"
                                       text="Tải mẫu file Excel" id="btn-download-material-template"></vc:button>
                            <vc:button variant="primary"
                                       icon-left="<div class='w-[24px] h-[24px] bg-white' style='-webkit-mask: url(/images/icons/upload-01.svg) no-repeat center; mask: url(/images/icons/upload-01.svg) no-repeat center;'></div>"
                                       text="Tải lên" id="btn-upload-material-template"></vc:button>
                            <input type="file" id="file-material" class="hidden">
                            <vc:button icon-left="+" text="Thêm sản phẩm" variant="primary" id="btn-add-material"
                                       css-class="h-full"></vc:button>
                        </div>

                    </div>
                    <div id="input-material"></div>
                    <partial name="Form/_FormMaterial"></partial>

                    <div class="flex justify-end !gap-4">
                        <vc:button text="Hủy" variant="secondary" size="lg" type="button"
                                   id="btn-material-cancel"
                                   css-class="h-[40px]"></vc:button>
                        <vc:button text="Lưu" variant="primary" type="submit" size="lg"
                                   id="btn-material-save"
                                   css-class="h-[40px]"></vc:button>
                    </div>
                </div>
            </form>
            <div id="content-rule" style="display: none" class="tab-content">
                <div id="div-preview-rule" class="w-full">
                    <div class="space-y-3 text-sm font-normal" style="letter-spacing: -0.15px">
                        <div class="flex flex-row">
                            <div class="w-1/3 text-grey-600">Rule list:</div>
                            <div class="w-2/3 text-gray-900">
                                @if (Model.RuleConfig != null && Model.RuleConfig.Rules is { Count: > 0 })
                                {
                                    foreach (var rule in Model.RuleConfig.Rules)
                                    {
                                        var curEnum = Enum.Parse<EnumProgram.SchemeRule>(rule);
                                        <div>@curEnum.GetEnumDisplayName()</div>
                                    }
                                }
                                else
                                {
                                    @("-")
                                }
                            </div>
                        </div>
                        <div id="div-rule-route" class="!mb-4">
                            <div class="flex items-center !mb-2 justify-between !pe-2">
                                <div class="flex items-center">
                                    <h2 class="text-xs font-semibold text-primary-500 tracking-wider">ROUTE
                                        TUYẾN</h2>
                                </div>
                            </div>
                            <div class="flex flex-row">
                                <div class="w-1/3 text-grey-600">Vùng:</div>
                                <div class="w-2/3 text-gray-900">
                                    @if (Model.SelectedRegions is { Count : > 0 })
                                    {
                                        foreach (var item in Model.SelectedRegions ?? [])
                                        {
                                            <div>@item.Text</div>
                                        }
                                    }
                                    else
                                    {
                                        @("-")
                                    }

                                </div>
                            </div>
                        </div>


                        <div id="div-rule-target" class="!mb-4">
                            <div class="flex items-center !mb-2 justify-between !pe-2">
                                <div class="flex items-center">
                                    <h2 class="text-xs font-semibold text-primary-500 tracking-wider">TARGET</h2>
                                </div>
                            </div>
                            <div class="flex flex-row">
                                <div class="w-1/3 text-grey-600">Số lượng yêu cầu:</div>
                                <div class="w-2/3 text-gray-900">
                                    @(Model.TargetValue?.ToString("G29") ?? "-")
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
                <div id="div-edit-rule" class="hidden">
                    <form action="@Url.Action("UpdateProgramRule")" id="form-rule">
                        <input type="hidden" id="ProgramId" name="ProgramId" value="@Model.ProgramId">
                        <partial name="Form/_FormRule" model="Model"></partial>
                        <div class="flex justify-end !gap-4">
                            <vc:button text="Hủy" variant="secondary" size="lg" type="button"
                                       id="btn-rule-cancel"
                                       css-class="h-[40px]"></vc:button>
                            <vc:button text="Lưu" variant="primary" type="submit" size="lg"
                                       id="btn-rule-save"
                                       css-class="h-[40px]"></vc:button>
                        </div>
                    </form>
                </div>
                <div class="flex justify-end !gap-4 hidden" id="div-fake-button">
                    <vc:button text="Hủy" variant="secondary" size="lg" type="button"
                               id="btn-rule-cancel-fake"
                               css-class="h-[40px]"></vc:button>
                    <vc:button text="Lưu" variant="primary" type="submit" size="lg"
                               id="btn-rule-save-fake"
                               css-class="h-[40px]"></vc:button>
                </div>
            </div>

        </div>
    </div>
</div>

@{
    var shopModal = new ModalModel
    {
        Id = "shop",
        Type = ModalModelType.Partial,
        CustomContent = "Modals/_AddShopModal",
        Title = "Thêm cửa hàng",
        IsVerticalAlign = true,
        Size = ModalModelSize.Large,
    };
    var materialModal = new ModalModel
    {
        Id = "material",
        Type = ModalModelType.Partial,
        CustomContent = "Modals/_AddMaterialModal",
        Title = "Thêm sản phẩm",
        IsVerticalAlign = true,
        Size = ModalModelSize.Large,
    };
}

@section Modals{
    <partial name="Components/_ModalForm" model="shopModal"/>
    <partial name="Components/_ModalForm" model="materialModal"/>
}


@section Scripts {
    <script src="~/js/xlsx.min.js"></script>
    <partial name="_ValidationScriptsPartialv2"></partial>
    <partial name="Scripts/_StatusScript"></partial>

    <script>
        @{
            var filePath = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images/icons/trash-01.svg");
        }
        const trashIconHtml = `@Html.Raw(await File.ReadAllTextAsync(filePath))`
        const getFileSize = (size) => {
            var _size = size;
            var fSExt = new Array('Bytes', 'KB', 'MB', 'GB'),
                i = 0;
            while (_size > 900) {
                _size /= 1024;
                i++;
            }
            var exactSize = (Math.round(_size * 100) / 100) + ' ' + fSExt[i];
            return exactSize;
        }
    </script>
    <script>
        var isDetailPage = true;
        var isEditing = false;
        const isEditable = @(Model.ProgramStatus != EnumProgram.Status.Pending ? "true" : "false")
        const cutOffAt = "@(Model.ShopCutOffAt?.ToString("yyyy-MM-dd") ?? "")"
        const isDraft = @(Model.ProgramStatus == EnumProgram.Status.Draft ? "true" : "false")
        DataTable.types().forEach(type => {
            DataTable.type(type, 'detect', () => false);
        });
        if ($("#StartAt").length > 0) {
            var startEndPicker = initDateRangePicker("startEndPicker", "StartAt", "EndAt");
        }
        if ($("#ShopCutOffAt").length > 0) {
            var cutoffPicker = initSingleDatePicker("cutoffPicker", "ShopCutOffAt");
        }
        const validateEditButton = () => {
            $("#div-fake-button").addClass("hidden");
            if (
                !isEditing
                && isEditable
                && (
                    $("#tab-program").hasClass("active")
                    || ($("#tab-rule").hasClass("active") && cutOffAt && moment(cutOffAt).isAfter(new Date(), "date"))
                )
            ) {
                $("#btn-update").show()
                // $("#btn-publish").show()
                $("#div-edit-program").addClass("hidden");
                $("#div-preview-program").removeClass("hidden");
                $("#div-edit-rule").addClass("hidden");
                $("#div-preview-rule").removeClass("hidden");
                if (($("#tab-rule").hasClass("active") && cutOffAt && moment(cutOffAt).isSameOrBefore(new Date(), "date"))) {
                    $("#div-fake-button").removeClass("hidden");
                }
                return true;
            } else {
                $("#btn-update").hide()
                // $("#btn-publish").hide()
                if (($("#tab-rule").hasClass("active") && cutOffAt && moment(cutOffAt).isSameOrBefore(new Date(), "date"))) {
                    $("#div-fake-button").removeClass("hidden");
                }
                return false;
            }
        }


        $(".tab-component a").click(function (e) {
            e.preventDefault();
            if (isEditing) {
                showModalConfirm({
                    title: "Xác nhận hủy",
                    htmlContent: "Bạn đang chỉnh sửa, chuyển qua tab này sẽ hủy các thay đổi của bạn. Bạn vẫn muốn chuyển tab?",
                    action: () => {
                        isEditing = false;
                        validateEditButton()
                        $(e.currentTarget).click()
                    }
                })
                return;
            }
            history.replaceState(null, null, this.hash);
            // window.location.hash = this.hash;
            $(".tab-component a").removeClass("active");
            $(e.target).addClass("active");
            $(".tab-content").hide()
            $($(e.target).attr("href")).show();
            validateEditButton()
        })


        const renderCellContent = (data) => {
            return `<div style="white-space: wrap;display: -webkit-box;-webkit-line-clamp: 4; line-clamp: 4; -webkit-box-orient: vertical; text-overflow: ellipsis;word-wrap: break-word;overflow-x: hidden;overflow-y: auto;">${data || "-"}</div>`
        }


        validateEditButton();

        $("#btn-update").click(function (e) {
            isEditing = true;
            validateEditButton();
            if ($("#tab-program").hasClass("active")) {
                $("#div-edit-program").removeClass("hidden");
                $("#div-preview-program").addClass("hidden");
            } else if ($("#tab-rule").hasClass("active")) {
                if (cutOffAt && moment(cutOffAt).isAfter(new Date(), "date")) {
                    $("#div-edit-rule").removeClass("hidden");
                    $("#div-preview-rule").addClass("hidden");
                }
            }
        })

        const cancelAction = () => {
            const confirmClass = `${buttonClasses.init} ${buttonClasses.size.lg} ${buttonClasses.color.danger}`
            showModalConfirm({
                title: "Hủy chỉnh sửa Scheme",
                htmlContent: `Bạn có muốn hủy chỉnh sửa scheme này không?
                <br>Nếu bấm "Xác nhận hủy" mọi thông tin bạn điền sẽ mất đi và không được lưu giữ`,
                action: () => {
                    // isEditing = false;
                    // validateEditButton();
                    window.location.reload()
                },
                confirmClass: confirmClass,
                cancelTitle: "Không",
                confirmTitle: "Xác nhận hủy"
            })
        }

        $("#btn-program-cancel").click(function (e) {
            cancelAction()
        })

        $("#btn-shop-cancel").click(function (e) {
            cancelAction()
        })

        $("#btn-material-cancel").click(function (e) {
            cancelAction()
        })

        $("#btn-rule-cancel").click(function (e) {
            cancelAction()
        })
        $("#btn-rule-cancel-fake").click(function (e) {
            cancelAction()
        })

        jQuery.validator.addMethod("selectType", function (value, element) {
            return $("#Status", $("#form-program")).val() != "-1";
        }, "Hãy nhập thông tin này");
        $("#form-program").find("#Status").rules("add", "selectType");

        $("#form-program").on("submit", function (e) {
            e.preventDefault();
            $("#form-program").valid()
            const validator = $("#form-program").validate();
            if (!validator.valid()) {
                return;
            }
            let curFormData = new FormData($("#form-program")[0]);
            showLoading({})
            $.ajax({
                url: "@Url.Action("UpdateProgramDetail")",
                type: 'POST',
                data: curFormData,
                success: function (data) {
                    showSuccess("Thành công");
                    setTimeout(function () {
                        window.location.reload()
                    }, 1000)
                },
                cache: false,
                contentType: false,
                processData: false,
                error: function (xhr) {
                    let data = xhr.responseJSON;
                    if (data) {
                        showError(data.errorMessage || data)
                    } else {
                        showError("Hãy thử lại sau hoặc liên hệ admin nhé.");
                    }
                },
                complete: function (data) {
                    hideLoading({})
                },
            });
        })
        $("#btn-rule-save-fake").click(function (e) {
            $("#form-program-other").trigger("submit");
        })
        $("#form-program-other").on("submit", function (e) {
            e.preventDefault();
            let programShops = [...tblShop.rows().data().toArray()];
            if (!programShops.length) {
                showModalConfirm({
                    confirmClass: "hidden",
                    title: "Thiếu thông tin cửa hàng",
                    htmlContent: "Cần có ít nhất 1 cửa hàng trong danh sách",
                })
                return;
            }
            if (programShops.some(e => !e.shop.isActive || e.shop.lockUntil)) {
                let shopCodes = [...programShops.filter(e => !e.shop.isActive || e.shop.lockUntil).map(e => e.shop.code)]
                showModalConfirm({
                    confirmClass: "hidden",
                    title: "Thông tin cửa hàng không hợp lệ",
                    htmlContent: "Các cửa hàng sau có thông tin không hợp lệ: " + shopCodes.join(", "),
                })
                return;
            }

            let programMaterials = [...tblMaterial.rows().data().toArray()];
            if (!programMaterials.length) {
                showModalConfirm({
                    confirmClass: "hidden",
                    title: "Thiếu thông tin sản phẩm",
                    htmlContent: "Cần có ít nhất 1 sản phẩm trong danh sách",
                })
                return;
            }

            let curFormData = new FormData($("#form-program-other")[0]);
            showLoading({})
            $.ajax({
                url: "@Url.Action("UpdateProgramOther")",
                type: 'POST',
                data: curFormData,
                success: function (data) {
                    showSuccess("Thành công");
                    setTimeout(function () {
                        window.location.reload()
                    }, 500)
                },
                cache: false,
                contentType: false,
                processData: false,
                error: function (xhr) {
                    let data = xhr.responseJSON;
                    if (data) {
                        showError(data.errorMessage || data)
                    } else {
                        showError("Hãy thử lại sau hoặc liên hệ admin nhé.");
                    }
                },
                complete: function (data) {
                    hideLoading({})
                },
            });
        })


        $("#form-rule").on("submit", function (e) {
            e.preventDefault();
            $("#form-rule").valid()
            const validator = $("#form-rule").validate();
            if (!validator.valid()) {
                return;
            }
            let curFormData = new FormData($("#form-rule")[0]);
            showLoading({})
            $.ajax({
                url: "@Url.Action("UpdateProgramRule")",
                type: 'POST',
                data: curFormData,
                success: function (data) {
                    showSuccess("Thành công");
                    setTimeout(function () {
                        window.location.reload()
                    }, 500)
                },
                cache: false,
                contentType: false,
                processData: false,
                error: function (xhr) {
                    let data = xhr.responseJSON;
                    if (data) {
                        showError(data.errorMessage || data)
                    } else {
                        showError("Hãy thử lại sau hoặc liên hệ admin nhé.");
                    }
                },
                complete: function (data) {
                    hideLoading({})
                },
            });
        })
        $(document).ready(function () {
            renderSelect2AjaxItem($("#AddMaterialId"), {
                excludeIds: () => {
                    return [...tblMaterial.rows().data().toArray().map(x => x.materialId)]
                }
            })
            renderSelect2AjaxItem($("#AddShopId"), {
                excludeIds: () => {
                    return [...tblShop.rows().data().toArray().map(x => x.shopId)]
                }
            })
        })

        // Export functions for Shop and Material datatables
        $("#btn-export-shop-excel").click(() => {
            exportShopExcel();
        })

        $("#btn-export-material-excel").click(() => {
            exportMaterialExcel();
        })

        const exportShopExcel = () => {
            try {
                // Show loading indicator
                $.LoadingOverlay("show", {
                    text: "Đang xuất file Excel...",
                    fade: 0,
                });

                // Get all shop data from the datatable
                const shopData = tblShop.rows().data().toArray();

                if (shopData.length === 0) {
                    $.LoadingOverlay("hide", {fade: 0});
                    showError("Không có dữ liệu để xuất");
                    return;
                }

                // Map data for Excel export
                const mappedData = shopData.map(row => ({
                    'Mã CH': row.shop?.code || '',
                    'Tên CH': row.shop?.name || '',
                    'KV KD': row.shop?.saleAreaDisplayName || '',
                    'Phân loại CH': row.shop?.shopGroupTypeName || '',
                    'Địa chỉ': row.shop?.address || '',
                    'Tỉnh/TP': row.shop?.provinceName || '',
                    'Vùng': row.shop?.provinceRegionName || '',
                    'Loại hình KD': row.shop?.businessTypeFmt || '',
                    'Tổng tiền TT': row.eWarrantyPrograms?.reduce((a, b) => a + b.price, 0) || 0,
                    'Tổng tiền TN': row.eWarrantyPrograms?.reduce((a, b) => a + b.actualPrice, 0) || 0,
                    'Trạng thái': row.statusDisplay || ''
                }));

                // Create Excel file using XLSX
                const worksheet = XLSX.utils.json_to_sheet(mappedData);

                // Set column widths
                const colWidths = Object.keys(mappedData[0]).map((key) => {
                    let maxLength = key.length;
                    mappedData.forEach(row => {
                        const cellValue = String(row[key] || '');
                        if (cellValue.length > maxLength) {
                            maxLength = cellValue.length;
                        }
                    });
                    return {wch: Math.min(maxLength + 2, 50)}; // Max width 50, min buffer 2
                });
                worksheet['!cols'] = colWidths;

                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "Danh sách cửa hàng");

                // Generate filename with current date
                const fileName = `DanhSachCuaHang_${moment().format("DD-MM-YYYY_HHmmss")}.xlsx`;

                // Download file
                XLSX.writeFile(workbook, fileName);

                // Hide loading and show success message
                setTimeout(() => {
                    $.LoadingOverlay("hide", {fade: 0});
                    showSuccess("Đã xuất file Excel thành công");
                }, 500);

            } catch (error) {
                console.error('Export error:', error);
                $.LoadingOverlay("hide", {fade: 0});
                showError("Có lỗi xảy ra khi xuất file Excel: " + error.message);
            }
        };

        const exportMaterialExcel = () => {
            try {
                // Show loading indicator
                $.LoadingOverlay("show", {
                    text: "Đang xuất file Excel...",
                    fade: 0,
                });

                // Get all material data from the datatable
                const materialData = tblMaterial.rows().data().toArray();

                if (materialData.length === 0) {
                    $.LoadingOverlay("hide", {fade: 0});
                    showError("Không có dữ liệu để xuất");
                    return;
                }

                // Map data for Excel export
                const mappedData = materialData.map(row => ({
                    'Mã SP': row.material?.code || '',
                    'Kiểu máy đồng bộ từ SAP': row.material?.model || '',
                    'Kiểu máy điều chỉnh': row.material?.modelTypeOtherName || '',
                    'Mã ngành hàng': row.material?.plantCode || '',
                    'Ngành hàng': row.material?.plantNameVn || '',
                    'Tiền thưởng': row.price || 0,
                    'Point': row.point || 0
                }));

                // Create Excel file using XLSX
                const worksheet = XLSX.utils.json_to_sheet(mappedData);

                // Set column widths
                const colWidths = Object.keys(mappedData[0]).map((key) => {
                    let maxLength = key.length;
                    mappedData.forEach(row => {
                        const cellValue = String(row[key] || '');
                        if (cellValue.length > maxLength) {
                            maxLength = cellValue.length;
                        }
                    });
                    return {wch: Math.min(maxLength + 2, 50)}; // Max width 50, min buffer 2
                });
                worksheet['!cols'] = colWidths;

                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "Danh sách sản phẩm");

                // Generate filename with current date
                const fileName = `DanhSachSanPham_${moment().format("DD-MM-YYYY_HHmmss")}.xlsx`;

                // Download file
                XLSX.writeFile(workbook, fileName);

                // Hide loading and show success message
                setTimeout(() => {
                    $.LoadingOverlay("hide", {fade: 0});
                    showSuccess("Đã xuất file Excel thành công");
                }, 500);

            } catch (error) {
                console.error('Export error:', error);
                $.LoadingOverlay("hide", {fade: 0});
                showError("Có lỗi xảy ra khi xuất file Excel: " + error.message);
            }
        };
    </script>
    <partial name="Scripts/_ShopDetailScript"></partial>
    <partial name="Scripts/_MaterialDetailScript"></partial>
    <script>
showLoading({});
callAjax({
    url: "@Url.Action("GetProgramDetail")/@Model.ProgramId",
    successCallback: function (data) {
        validateAndAddShop([...data.programShops], true)
        validateAndAddMaterial([...data.programMaterials], true)
        hideLoading({});
    },
    errorCallback: function (xhr) {
        hideLoading({});
    },
    isShowMessage: false,
})
        let curStatus = renderStatus(@Model.ProgramStatus.GetEnumValue(), {
            statusDisplay: "@Model.StatusDisplay",
            startAt: "@Model.StartAt.ToString("yyyy-MM-dd")",
            endAt: "@Model.EndAt.ToString("yyyy-MM-dd")",
        }, null, false)
        $("#div-status").html(curStatus)

        $(document).ready(function () {
            $(window).on("hashchange", function (e) {
                e.preventDefault()
                var tabhash = location.hash.replace(/^#/, '');
                if (tabhash) {
                    $('#tab-list a[href="#' + tabhash + '"]').click();
                    // let scrollmem = $('body').scrollTop() ;
                    // $('html,body').scrollTop(0);

                } else {
                    $("#tab-program").click()
                }
            }).trigger("hashchange")
            // .trigger("hashchange");
        })
    </script>
}
