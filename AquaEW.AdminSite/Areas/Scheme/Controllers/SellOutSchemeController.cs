﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Mio.Core;
using Mio.Core.ViewModel;
using Mio.Core.ViewModel.PagingViewModel;
using Mio.Services.Interfaces;
using AquaEW.AdminSite.Helper;
using AquaEW.AdminSite.PermissionAccess;
using AquaEW.Core;
using AquaEW.Core.Const;
using AquaEW.Core.EnumModel;
using AquaEW.Core.ViewModel;
using AquaEW.Core.ViewModel.Common.Notifications;
using AquaEW.Core.ViewModel.Scheme;
using AquaEW.Services.Interfaces;
using AquaEW.Services.Interfaces.Common.Files;
using AquaEW.Services.Interfaces.Common.Notifications;
using ClosedXML.Excel;
using LinqKit;
using Mio.Core.EnumModel;
using Mio.Core.Models;

namespace AquaEW.AdminSite.Areas.Scheme.Controllers
{
    [Area("Scheme")]
    [BasedAuthentication]
    [SessionValidation]
    [MioController("Quản lý tính thưởng BH", "")]
    [MioArea("Quản lý tính thưởng")]
    public class SellOutSchemeController(
        IMemoryCache memoryCache,
        IProgramService programService,
        IShopService shopService,
        IMaterialService materialService,
        IProgramShopService programShopService,
        IProgramMaterialService programMaterialService,
        IRegionService regionService,
        IEwNotificationService ewNotificationService
    ) : MioBaseController(memoryCache: memoryCache)
    {
        private readonly IEwNotificationService _ewNotificationService = ewNotificationService;

        [HasPermission(EnumPermission.OwnAction.View)]
        public IActionResult Index()
        {
            return View();
        }

        [HasPermission(EnumPermission.OwnAction.View, isAjax: true)]
        public PaginatedModel<ProgramViewModel> GetAllPrograms(
            [FromForm] DataTableParameters parameter,
            DateTime? startDate = null,
            DateTime? endDate = null
        )
        {
            var condition = PredicateBuilder.New<Mio.Core.Models.Program>(x =>
                x.IsDeleted == false
            );
            if (startDate != null)
            {
                condition = condition.And(x => x.CreatedAt != null && x.CreatedAt >= startDate);
            }

            if (endDate != null)
            {
                endDate = endDate?.Date.AddDays(1).AddTicks(-1);
                condition = condition.And(x => x.CreatedAt != null && x.CreatedAt <= endDate);
            }

            if (parameter != null)
            {
                var curCreatedAt = parameter.Columns.FirstOrDefault(x => x.Data == "createdAt");
                if (curCreatedAt != null)
                {
                    if (!string.IsNullOrWhiteSpace(curCreatedAt.Search.Value) && DateTime.TryParse(curCreatedAt.Search.Value, out var curDate))
                    {
                        curDate = curDate.Date;
                        condition = condition.And(x => x.CreatedAt.Date == curDate.Date);
                    }
                }

                var curApproveAt = parameter.Columns.FirstOrDefault(x => x.Data == "approveAt");
                if (curApproveAt != null)
                {
                    if (!string.IsNullOrWhiteSpace(curApproveAt.Search.Value) && DateTime.TryParse(curApproveAt.Search.Value, out var curDate))
                    {
                        curDate = curDate.Date;
                        condition = condition.And(x => x.ApproveAt != null && x.ApproveAt.Value.Date == curDate.Date);
                    }
                }

                var curStatus = parameter.Columns.FirstOrDefault(x => x.Data == "programStatus");
                if (curStatus != null)
                {
                    if (!string.IsNullOrWhiteSpace(curStatus.Search.Value) && Enum.TryParse<EnumProgram.Status>(curStatus.Search.Value, out var status))
                    {
                        if (status == EnumProgram.Status.Active)
                        {
                            condition = condition.And(x => x.ProgramStatus == EnumProgram.Status.Active && DateTime.Today <= x.EndAt);
                        }
                        else if (status == EnumProgram.Status.Inactive)
                        {
                            condition = condition.And(x => x.ProgramStatus == EnumProgram.Status.Inactive || (x.ProgramStatus == EnumProgram.Status.Active && DateTime.Today > x.EndAt));
                        }
                        else
                        {
                            condition = condition.And(x => x.ProgramStatus == status);
                        }
                    }
                }

                var curType = parameter.Columns.FirstOrDefault(x => x.Data == "type");
                if (curType != null)
                {
                    if (!string.IsNullOrWhiteSpace(curType.Search.Value))
                    {
                        if (curType.Search.Value == "Normal")
                        {
                            condition = condition.And(x => x.Status == null || x.Status == "");
                        }
                        else
                        {
                            condition = condition.And(x => x.Status != null && x.Status != "");
                        }
                    }
                }
            }


            var result = programService.GetPaginatedListFromDatatable(
                param: parameter
                , predicate: condition
                , orders: x => x.OrderByDescending(sr => sr.ProgramId)
                , asSplitQuery: true
                , asNoTracking: true
            );
            return result;
        }

        public IActionResult Create()
        {
            return View("Form", new ProgramViewModel { IsCreate = true });
        }

        [HttpPost]
        public async Task<IActionResult> Create(ProgramViewModel model)
        {
            var result = await programService.CreateProgram(model, CurrentUser);
            Response.StatusCode = result.StatusCode;

            // Create EW notification for program creation if successful
            if (result.IsSuccess && result.Data != null)
            {
                try
                {
                    var notification = CreateEwNotificationForProgram(result.Data, "created");
                    if (notification != null)
                    {
                        await _ewNotificationService.Create(notification);
                    }
                }
                catch (Exception)
                {
                    // Don't throw exception here as notification failure shouldn't break the main process
                }
            }

            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        public IActionResult Detail(int id)
        {
            var program = programService.FirstOrDefault(x =>
                    x.ProgramId == id
                , includes: x => x
                    .Include(p => p.ProgramShops)
                    .Include(p => p.ProgramMaterials));
            if (program == null)
            {
                return View("Error", new ErrorViewModel { Error = $"Không tìm thấy chương trình với id {id}" });
            }

            if (program.RuleConfig?.RouteRegions != null && program.RuleConfig.RouteRegions.Count > 0)
            {
                // var regionIds = program.RuleConfig.RouteRegions.Select(x => int.Parse(x)).ToList();
                // program.SelectedRegions = regionService.Filter(x => regionIds.Contains(x.RegionId)).Select(x => new Select2ViewModel()
                // {
                //     Id = x.RegionId.ToString(),
                //     Text = x.Name
                // }).ToList();
                program.SelectedRegions = program.RuleConfig.RouteRegions.Select(x => new Select2ViewModel()
                {
                    Id = x,
                    Text = x,
                }).ToList();
            }

            if (program.ProgramStatus == EnumProgram.Status.Pending)
            {
                return View("DetailApprove", program);
            }

            return View(program);
        }

        public IActionResult Update(int id)
        {
            var program = programService.FirstOrDefault(x =>
                x.ProgramId == id
            );
            if (program == null)
            {
                return View("Error", new ErrorViewModel { Error = $"Không tìm thấy chương trình với id {id}" });
            }

            if (program.RuleConfig?.RouteRegions != null && program.RuleConfig.RouteRegions.Count > 0)
            {
                // var regionIds = program.RuleConfig.RouteRegions.Select(x => int.Parse(x)).ToList();
                // program.SelectedRegions = regionService.Filter(x => regionIds.Contains(x.RegionId)).Select(x => new Select2ViewModel()
                // {
                //     Id = x.RegionId.ToString(),
                //     Text = x.Name
                // }).ToList();
                program.SelectedRegions = program.RuleConfig.RouteRegions.Select(x => new Select2ViewModel()
                {
                    Id = x,
                    Text = x,
                }).ToList();
            }

            return View("Form", program);
        }

        [HttpPost]
        public async Task<IActionResult> Update(ProgramViewModel model)
        {
            var result = await programService.UpdateProgram(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateProgramDetail(ProgramViewModel model)
        {
            var result = await programService.UpdateProgramDetail(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateProgramShop(ProgramViewModel model)
        {
            var result = await programService.UpdateProgramShop(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateProgramMaterial(ProgramViewModel model)
        {
            var result = await programService.UpdateProgramMaterial(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }


        [HttpPost]
        public async Task<IActionResult> UpdateProgramRule(ProgramViewModel model)
        {
            var result = await programService.UpdateProgramRule(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateProgramOther(ProgramViewModel model)
        {
            var result = await programService.UpdateProgramOther(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        [HttpPost]
        public async Task<IActionResult> Publish(int programId)
        {
            var result = await programService.UpdateStatus(new ProgramViewModel
            {
                ProgramId = programId,
                ProgramStatus = EnumProgram.Status.Active,
            }, CurrentUser);
            Response.StatusCode = result.Code;

            // Create EW notification for program publishing if successful
            if (result.IsSuccess)
            {
                try
                {
                    var program = programService.FirstOrDefault(x => x.ProgramId == programId);
                    if (program != null)
                    {
                        var notification = CreateEwNotificationForProgram(program, "published");
                        if (notification != null)
                        {
                            await _ewNotificationService.Create(notification);
                        }
                    }
                }
                catch (Exception)
                {
                    // Don't throw exception here as notification failure shouldn't break the main process
                }
            }

            return Json(result.Message);
        }

        [HttpPost]
        public async Task<IActionResult> UpdateStatus(ProgramViewModel model)
        {
            var result = await programService.UpdateStatus(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.Message);
        }


        [HttpPost]
        public async Task<IActionResult> Deactivate(int programId)
        {
            var result = await programService.UpdateStatus(new ProgramViewModel
            {
                ProgramId = programId,
                ProgramStatus = EnumProgram.Status.Inactive,
            }, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.Message);
        }

        [HttpPost]
        public async Task<IActionResult> Activate(int programId)
        {
            var result = await programService.UpdateStatus(new ProgramViewModel
            {
                ProgramId = programId,
                ProgramStatus = EnumProgram.Status.Active,
            }, CurrentUser);
            Response.StatusCode = result.Code;

            // Create EW notification for program activation if successful
            if (result.IsSuccess)
            {
                try
                {
                    var program = programService.FirstOrDefault(x => x.ProgramId == programId);
                    if (program != null)
                    {
                        var notification = CreateEwNotificationForProgram(program, "activated");
                        if (notification != null)
                        {
                            await _ewNotificationService.Create(notification);
                        }
                    }
                }
                catch (Exception)
                {
                    // Don't throw exception here as notification failure shouldn't break the main process
                }
            }

            return Json(result.Message);
        }

        public async Task<IActionResult> GetProgramDetail(int id)
        {
            var programShops = programShopService.Filter(x =>
                    x.ProgramId == id
                , includes: x => x
                    .Include(ps => ps.Shop).ThenInclude(s => s.ShopGroupType)
                    .Include(ps => ps.Shop).ThenInclude(s => s.Province).ThenInclude(p => p.Region)
                    .Include(ps => ps.EWarrantyPrograms)
            ).ToList();
            var programMaterial = programMaterialService.Filter(x =>
                    x.ProgramId == id
                , includes: x => x
                    .Include(pm => pm.Material).ThenInclude(m => m.Plant)
            ).ToList();
            return Json(new ProgramViewModel { ProgramShops = programShops, ProgramMaterials = programMaterial });
        }

        [HttpPost]
        public async Task<IActionResult> RemoveMaterial(ProgramMaterialViewModel model)
        {
            var result = await programService.RemoveMaterial(model, CurrentUser);
            Response.StatusCode = result.Code;
            return Json(result.IsSuccess ? result.SuccessMessage : result.ErrorMessage);
        }

        public IActionResult GetAllShopsAsSelect2(string keyword, int page, string excludeIds = null)
        {
            excludeIds ??= "";
            List<int> excludeIdList = string.IsNullOrWhiteSpace(excludeIds) ? [] : excludeIds.Split(',').Select(int.Parse).ToList();
            // page = page <= 0 ? 1 : page;
            Dictionary<string, string[]> query = new()
            {
                { "page", [page.ToString()] },
                { "limit", ["20"] },
                { "Name_od", ["asc"] },
            };
            var result = shopService.GetAllItemAsSelect2(
                query: query
                , idSelector: x => x.ShopId
                , nameSelector: x => $"[{x.Code}] {x.Name}"
                , predicate: x =>
                    x.IsDeleted == false
                    && (
                        EF.Functions.ILike(x.Code, $"{keyword}%")
                        || EF.Functions.ILike(x.Name, $"{keyword}%")
                    )
                    && !excludeIdList.Contains(x.ShopId)
                , asNoTracking: true
            );
            if (page == 1)
            {
                result.Result.Insert(0, new Select2ViewModel
                {
                    Id = "",
                    Text = "- Bỏ chọn -"
                });
            }

            return Json(result);
        }

        public IActionResult GetAllMaterialsAsSelect2(string keyword, int page, string excludeIds = null)
        {
            excludeIds ??= "";
            List<int> excludeIdList = string.IsNullOrWhiteSpace(excludeIds) ? [] : excludeIds.Split(',').Select(int.Parse).ToList();
            Dictionary<string, string[]> query = new()
            {
                { "page", [page.ToString()] },
                { "limit", ["20"] },
                { "Code_od", ["asc"] },
            };
            var result = materialService.GetAllItemAsSelect2(
                query: query
                , idSelector: x => x.MaterialId
                , nameSelector: x => $"[{x.Code}] {x.ModelTypeOtherName}"
                , predicate: x =>
                    x.IsDeleted == false
                    && x.Code != null
                    && x.Code != ""
                    && (
                        EF.Functions.ILike(x.Code, $"%{keyword}%")
                        || EF.Functions.ILike(x.ModelTypeOtherName, $"%{keyword}%")
                    )
                    && !excludeIdList.Contains(x.MaterialId)
                , asNoTracking: true
            );

            return Json(result);
        }

        public IActionResult GetAllRegionsAsSelect2(string keyword, int page)
        {
            Dictionary<string, string[]> query = new()
            {
                { "page", [page.ToString()] },
                { "limit", ["20"] },
                { "Code_od", ["asc"] },
            };
            var result = regionService.GetAllItemAsSelect2(
                query: query
                , idSelector: x => x.RegionId
                , nameSelector: x => $"{x.Name}"
                , predicate: x =>
                    x.IsDeleted == false
                    && (
                        EF.Functions.ILike(x.Name, $"%{keyword}%")
                    )
                , asNoTracking: true
                , otherSelector: x => x
            );

            return Json(result);
        }

        public IActionResult GetShopsByCode(List<string> shopCodes)
        {
            var result = shopService.Filter(x =>
                    x.IsDeleted == false
                    && shopCodes.Contains(x.Code)
                , includes: x => x
                    .Include(s => s.ShopGroupType)
                    .Include(s => s.Province).ThenInclude(p => p.Region)
            );

            return Json(result);
        }

        public IActionResult GetShopsById(List<int> ids)
        {
            var result = shopService.Filter(x =>
                    x.IsDeleted == false
                    && ids.Contains(x.ShopId)
                , includes: x => x
                    .Include(s => s.ShopGroupType)
                    .Include(s => s.Province).ThenInclude(p => p.Region)
            );

            return Json(result);
        }

        public IActionResult GetMaterialsByCode(List<string> materialCodes)
        {
            var result = materialService.Filter(x =>
                    x.IsDeleted == false
                    && materialCodes.Contains(x.Code)
                , includes: x => x
                    .Include(s => s.Plant)
            );

            return Json(result);
        }

        public IActionResult GetMaterialsById(List<int> ids)
        {
            var result = materialService.Filter(x =>
                    x.IsDeleted == false
                    && ids.Contains(x.MaterialId)
                , includes: x => x
                    .Include(s => s.Plant)
            );

            return Json(result);
        }

        [HttpPost]
        public IActionResult ExportShops([FromForm] SellOutSchemeShopExportFilterViewModel model)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(model.SheetName))
                {
                    model.SheetName = $"DanhSachCuaHang_{DateTime.Now:dd-MM-yyyy}";
                }

                // Get all shops with necessary includes
                var shops = shopService.Filter(x => x.IsDeleted == false,
                    includes: x => x
                        .Include(s => s.ShopGroupType)
                        .Include(s => s.Province).ThenInclude(p => p.Region)
                ).ToList();

                // Apply column-specific searches (in-memory filtering)
                var columnSearches = model.GetColumnSearches();
                if (columnSearches != null && columnSearches.Any())
                {
                    foreach (var columnSearch in columnSearches.Where(cs => !string.IsNullOrWhiteSpace(cs.SearchValue)))
                    {
                        var searchValue = columnSearch.SearchValue.Trim().ToLower();

                        switch (columnSearch.ColumnIndex)
                        {
                            case 0: // Code
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.Code?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.Code?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 1: // Name
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.Name?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.Name?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 2: // SaleArea
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.SaleArea?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.SaleArea?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 3: // ShopGroupTypeName
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.ShopGroupTypeName?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.ShopGroupTypeName?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 4: // Address
                                shops = shops.Where(s => s.Address?.ToLower().Contains(searchValue) == true).ToList();
                                break;
                            case 5: // ProvinceName
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.ProvinceName?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.ProvinceName?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 6: // ProvinceRegionName
                                if (columnSearch.IsRegex)
                                {
                                    var pattern = searchValue.Replace("^", "");
                                    shops = shops.Where(s => s.ProvinceRegionName?.ToLower().StartsWith(pattern) == true).ToList();
                                }
                                else
                                {
                                    shops = shops.Where(s => s.ProvinceRegionName?.ToLower().Contains(searchValue) == true).ToList();
                                }
                                break;
                            case 7: // BusinessTypeFmt
                                // Skip this for now as BusinessType enum display name filtering is complex
                                break;
                        }
                    }
                }

                // Apply global search if provided (in-memory filtering)
                if (!string.IsNullOrWhiteSpace(model.GlobalSearch))
                {
                    var globalSearch = model.GlobalSearch.Trim().ToLower();
                    shops = shops.Where(s =>
                        (s.Code?.ToLower().Contains(globalSearch) == true) ||
                        (s.Name?.ToLower().Contains(globalSearch) == true) ||
                        (s.SaleArea?.ToLower().Contains(globalSearch) == true) ||
                        (s.Address?.ToLower().Contains(globalSearch) == true) ||
                        (s.ShopGroupTypeName?.ToLower().Contains(globalSearch) == true) ||
                        (s.ProvinceName?.ToLower().Contains(globalSearch) == true) ||
                        (s.ProvinceRegionName?.ToLower().Contains(globalSearch) == true)
                    ).ToList();
                }

                // Apply ordering (in-memory sorting)
                var orders = model.GetOrders();
                if (orders != null && orders.Any())
                {
                    var firstOrder = orders.First();
                    switch (firstOrder.ColumnIndex)
                    {
                        case 0: // Code
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.Code).ToList() : shops.OrderByDescending(s => s.Code).ToList();
                            break;
                        case 1: // Name
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.Name).ToList() : shops.OrderByDescending(s => s.Name).ToList();
                            break;
                        case 2: // SaleArea
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.SaleArea).ToList() : shops.OrderByDescending(s => s.SaleArea).ToList();
                            break;
                        case 3: // ShopGroupTypeName
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.ShopGroupTypeName).ToList() : shops.OrderByDescending(s => s.ShopGroupTypeName).ToList();
                            break;
                        case 4: // Address
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.Address).ToList() : shops.OrderByDescending(s => s.Address).ToList();
                            break;
                        case 5: // ProvinceName
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.ProvinceName).ToList() : shops.OrderByDescending(s => s.ProvinceName).ToList();
                            break;
                        case 6: // ProvinceRegionName
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.ProvinceRegionName).ToList() : shops.OrderByDescending(s => s.ProvinceRegionName).ToList();
                            break;
                        case 7: // BusinessTypeFmt
                            shops = firstOrder.Direction == "asc" ? shops.OrderBy(s => s.BusinessType).ToList() : shops.OrderByDescending(s => s.BusinessType).ToList();
                            break;
                        default:
                            shops = shops.OrderBy(s => s.Code).ToList();
                            break;
                    }
                }
                else
                {
                    shops = shops.OrderBy(s => s.Code).ToList();
                }

                // Convert to export view model
                var exportData = shops.Select(s => new SellOutSchemeShopExportViewModel
                {
                    Code = s.Code ?? "",
                    Name = s.Name ?? "",
                    SaleArea = s.SaleArea ?? "",
                    ShopGroupTypeName = s.ShopGroupTypeName ?? "",
                    Address = s.Address ?? "",
                    ProvinceName = s.ProvinceName ?? "",
                    ProvinceRegionName = s.ProvinceRegionName ?? "",
                    BusinessTypeFmt = s.BusinessTypeFmt ?? "",
                    ActivityStatus = s.ActivityStatus ?? ""
                }).ToList();

                // Generate Excel file using the existing pattern
                var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add(model.SheetName);

                // Add headers
                var properties = typeof(SellOutSchemeShopExportViewModel).GetProperties();
                for (int i = 0; i < properties.Length; i++)
                {
                    var displayAttribute = properties[i].GetCustomAttributes(typeof(DisplayAttribute), false).FirstOrDefault() as DisplayAttribute;
                    var headerName = displayAttribute?.Name ?? properties[i].Name;
                    worksheet.Cell(1, i + 1).Value = headerName;
                    worksheet.Cell(1, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(1, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                }

                // Add data
                for (int row = 0; row < exportData.Count; row++)
                {
                    for (int col = 0; col < properties.Length; col++)
                    {
                        var value = properties[col].GetValue(exportData[row])?.ToString() ?? "";
                        worksheet.Cell(row + 2, col + 1).Value = value;
                    }
                }

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                // Generate file
                using var stream = new MemoryStream();
                workbook.SaveAs(stream);
                var content = stream.ToArray();

                return File(
                    content,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"{model.SheetName}.xlsx"
                );
            }
            catch (Exception ex)
            {
                Response.StatusCode = 500;
                return Json(new { success = false, message = $"Lỗi khi xuất file: {ex.Message}" });
            }
        }



        /// <summary>
        /// Create EW notification for Program status changes
        /// </summary>
        private EwNotificationViewModel CreateEwNotificationForProgram(ProgramViewModel program, string statusName)
        {
            string title = "";
            string content = "";

            switch (statusName)
            {
                case "created":
                    title = "Chương trình mới được tạo";
                    content = $"Chương trình '{program.Name}' đã được tạo mới. Vui lòng kiểm tra thông tin chi tiết.";
                    break;

                case "published":
                case "activated":
                    title = "Chương trình được kích hoạt";
                    content = $"Chương trình '{program.Name}' đã được kích hoạt. Vui lòng kiểm tra thông tin chi tiết.";
                    break;

                default:
                    return null; // Unknown status, don't send notification
            }

            return new EwNotificationViewModel
            {
                Title = title,
                Content = content,
                NotifyType = AquaEW.Core.SysEnum.NotificationType.Program,
                IsActive = true,
                IsDeleted = false,
                CreatedAt = DateTime.Now,
                ShopIds = new List<int>(), // Send to all shops - empty list means broadcast
                Domain = "Program",
                RecordId = program.ProgramId
            };
        }
    }
}
