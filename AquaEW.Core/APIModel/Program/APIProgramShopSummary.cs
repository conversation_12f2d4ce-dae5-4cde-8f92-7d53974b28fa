using System.Collections.Generic;
using System.Linq;
using AquaEW.Core.EnumModel;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Newtonsoft.Json;

namespace AquaEW.Core.APIModel.Program;

public class APIProgramShopSummary
{
    public string Title { get; set; }
    public decimal? Total => ProgramShops.Any(x => x.CalculatedAmount.HasValue) 
        ? ProgramShops.Sum(x => x.CalculatedAmount ?? 0) 
        : null;
    public decimal? ViolationFee => null;
    public decimal? DeductionAmount => null;
    public decimal? CalculatedAmount => ProgramShops.Any(x => x.CalculatedAmount.HasValue) 
        ? ProgramShops.Sum(x => x.CalculatedAmount ?? 0) 
        : null;
    public decimal? ActualAmount => 
        ProgramShops.Any(x => x.Status != EnumProgram.ProgramShopStatus.RewardApproved && x.ActualAmount.HasValue)
        ? ProgramShops.Sum(x => 
            x.Status != EnumProgram.ProgramShopStatus.RewardApproved ? (x?.ActualAmount ?? 0) : 0)
        : null;
    [JsonIgnore] public List<APISlimProgramShopViewModel> ProgramShops { get; set; }
}